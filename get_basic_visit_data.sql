-- Simple query to get basic visit data
-- This query provides essential visit information without complex aggregations

USE [MilkNotes_local]
GO

SELECT 
    -- Visit identification and timing
    v.Id AS VisitId,
    v.StartDateTime,
    v.EndDateTime,
    v.ClosedDateTime,
    v.CreateDate AS VisitCreateDate,
    
    -- Visit status
    vt.Name AS VisitType,
    vs.Name AS VisitStatus,
    v.IsCancelled,
    v.IsIntakeComplete,
    
    -- Parent information
    CONCAT(parent_names.First, ' ', parent_names.Last) AS ParentName,
    parent_persons.BirthDate AS ParentBirthDate,
    
    -- Consultant information  
    CONCAT(consultant_names.First, ' ', consultant_names.Last) AS ConsultantName,
    
    -- Appointment details
    at.Name AS AppointmentType,
    cl.Name AS ConsultLocation,
    v.Price,
    
    -- Payment method
    CASE 
        WHEN v.IsInsurance = 1 THEN 'Insurance'
        WHEN v.IsMedicaid = 1 THEN 'Medicaid'
        WHEN v.IsHsaFsa = 1 THEN 'HSA/FSA'
        WHEN v.IsSelfPay = 1 THEN 'Self Pay'
        ELSE 'Unknown'
    END AS PaymentMethod,
    
    -- Key notes
    v.ParentConcerns,
    v.MedicalReportNotes,
    
    -- Distance and duration
    v.DistanceTraveled,
    DATEDIFF(MINUTE, v.StartDateTime, v.EndDateTime) AS VisitDurationMinutes

FROM [dbo].[Visits] v
    
    -- Essential joins
    LEFT JOIN [dbo].[VisitTypes] vt ON v.VisitTypeId = vt.Id
    LEFT JOIN [dbo].[VisitStatuses] vs ON v.VisitStatusId = vs.Id
    
    -- Parent information
    LEFT JOIN [dbo].[Persons] parent_persons ON v.ParentId = parent_persons.Id
    LEFT JOIN [dbo].[Names] parent_names ON parent_persons.Id = parent_names.Id
    
    -- Consultant information
    LEFT JOIN [dbo].[Persons] consultant_persons ON v.ConsultantId = consultant_persons.Id
    LEFT JOIN [dbo].[Names] consultant_names ON consultant_persons.Id = consultant_names.Id
    
    -- Appointment and location
    LEFT JOIN [dbo].[AppointmentTypes] at ON v.AppointmentTypeId = at.Id
    LEFT JOIN [dbo].[ConsultLocations] cl ON at.ConsultLocationId = cl.Id

ORDER BY v.StartDateTime DESC;
