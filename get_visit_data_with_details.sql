-- Query to get all visit data with detailed child information, notes, and evaluations
-- This query provides a comprehensive view including child concerns, notes, and evaluation data

USE [MilkNotes_local]
GO

-- Main visit data with basic information
WITH VisitData AS (
    SELECT 
        -- Visit basic information
        v.Id AS VisitId,
        v.StartDateTime,
        v.EndDateTime,
        v.ClosedDateTime,
        v.CreateDate AS VisitCreateDate,
        v.EditDate AS VisitEditDate,
        v.EditReason,
        v.DistanceTraveled,
        v.<PERSON>,
        v.PostBuffer,
        v.PreBuffer,
        
        -- Visit status and type information
        vt.Name AS VisitType,
        vs.Name AS VisitStatus,
        vas.Name AS VisitAcceptanceStatus,
        v.VisitNotAcceptedReason,
        v.Is<PERSON>ancelled,
        vcr.Name AS VisitCancellationReason,
        v.VisitCancellationOtherReason,
        v.IsIntakeComplete,
        
        -- Payment information
        v.IsHsaFsa,
        v.IsInsurance,
        v.<PERSON>,
        v.<PERSON><PERSON>,
        spt.Name AS SelfPayType,
        
        -- HIPAA and communication
        v.HasAgreedToHipaaTerms,
        v.<PERSON>o<PERSON>ipaaConsent,
        ct.Name AS PreferredCommunicationType,
        
        -- Parent information
        parent_names.First AS ParentFirstName,
        parent_names.Middle AS ParentMiddleName,
        parent_names.Last AS ParentLastName,
        parent_persons.BirthDate AS ParentBirthDate,
        parent_genders.Name AS ParentGender,
        v.ParentHistoryNotes,
        v.ParentProfileNotes,
        v.ParentConcerns,
        
        -- Consultant information
        consultant_names.First AS ConsultantFirstName,
        consultant_names.Middle AS ConsultantMiddleName,
        consultant_names.Last AS ConsultantLastName,
        
        -- Appointment type information
        at.Name AS AppointmentType,
        at.Description AS AppointmentDescription,
        at.DefaultDuration AS AppointmentDefaultDuration,
        cl.Name AS ConsultLocation,
        
        -- Visit notes
        v.MedicalReportNotes,
        
        -- Subscriber information
        s.Id AS SubscriberId

    FROM [dbo].[Visits] v
        
        -- Join with visit lookup tables
        LEFT JOIN [dbo].[VisitTypes] vt ON v.VisitTypeId = vt.Id
        LEFT JOIN [dbo].[VisitStatuses] vs ON v.VisitStatusId = vs.Id
        LEFT JOIN [dbo].[VisitAcceptanceStatuses] vas ON v.VisitAcceptanceStatusId = vas.Id
        LEFT JOIN [dbo].[VisitCancellationReasons] vcr ON v.VisitCancellationReasonId = vcr.Id
        LEFT JOIN [dbo].[SelfPayTypes] spt ON v.SelfPayTypeId = spt.Id
        LEFT JOIN [dbo].[CommunicationTypes] ct ON v.PreferredCommunicationTypeId = ct.Id
        
        -- Join with parent information
        LEFT JOIN [dbo].[Persons] parent_persons ON v.ParentId = parent_persons.Id
        LEFT JOIN [dbo].[Names] parent_names ON parent_persons.Id = parent_names.Id
        LEFT JOIN [dbo].[Genders] parent_genders ON parent_persons.GenderId = parent_genders.Id
        
        -- Join with consultant information
        LEFT JOIN [dbo].[Persons] consultant_persons ON v.ConsultantId = consultant_persons.Id
        LEFT JOIN [dbo].[Names] consultant_names ON consultant_persons.Id = consultant_names.Id
        
        -- Join with appointment type and location
        LEFT JOIN [dbo].[AppointmentTypes] at ON v.AppointmentTypeId = at.Id
        LEFT JOIN [dbo].[ConsultLocations] cl ON at.ConsultLocationId = cl.Id
        
        -- Join with subscriber
        LEFT JOIN [dbo].[Subscribers] s ON v.SubscriberId = s.Id
),

-- Visit Notes aggregated
VisitNotesAgg AS (
    SELECT 
        vn.VisitId,
        STRING_AGG(CAST(vn.Note AS NVARCHAR(MAX)), '; ') AS AllVisitNotes,
        COUNT(*) AS VisitNotesCount
    FROM [dbo].[VisitNotes] vn
    GROUP BY vn.VisitId
),

-- Child Notes aggregated
ChildNotesAgg AS (
    SELECT 
        vcn.VisitId,
        STRING_AGG(CAST(vcn.Note AS NVARCHAR(MAX)), '; ') AS AllChildNotes,
        COUNT(*) AS ChildNotesCount
    FROM [dbo].[VisitChildNotes] vcn
    GROUP BY vcn.VisitId
),

-- Child Concerns aggregated
ChildConcernsAgg AS (
    SELECT 
        vcc.VisitId,
        STRING_AGG(CAST(cc.Name AS NVARCHAR(MAX)), '; ') AS AllChildConcerns,
        COUNT(*) AS ChildConcernsCount
    FROM [dbo].[VisitChildConcerns] vcc
    LEFT JOIN [dbo].[ChildConcerns] cc ON vcc.ChildConcernId = cc.Id
    GROUP BY vcc.VisitId
),

-- Comprehensive Evaluation Data
EvaluationsAgg AS (
    SELECT
        e.VisitId,
        COUNT(DISTINCT e.Id) AS EvaluationsCount,

        -- Kid Evaluation Data
        STRING_AGG(CAST(CONCAT(
            CASE WHEN ke.StoolsPerDay IS NOT NULL THEN 'Stools/day: ' + CAST(ke.StoolsPerDay AS VARCHAR) + '; ' ELSE '' END,
            CASE WHEN ke.VoidsPerDay IS NOT NULL THEN 'Voids/day: ' + CAST(ke.VoidsPerDay AS VARCHAR) + '; ' ELSE '' END,
            CASE WHEN ke.NumFeedsPerDay IS NOT NULL THEN 'Feeds/day: ' + CAST(ke.NumFeedsPerDay AS VARCHAR) + '; ' ELSE '' END,
            CASE WHEN ke.NumExpressionsPerDay IS NOT NULL THEN 'Expressions/day: ' + CAST(ke.NumExpressionsPerDay AS VARCHAR) + '; ' ELSE '' END,
            CASE WHEN ke.FeedingDurationMinutes IS NOT NULL THEN 'Feed Duration: ' + ke.FeedingDurationMinutes + ' min; ' ELSE '' END,
            CASE WHEN ke.FeedingFrequencyHours IS NOT NULL THEN 'Feed Frequency: ' + ke.FeedingFrequencyHours + ' hrs; ' ELSE '' END,
            CASE WHEN st.Name IS NOT NULL THEN 'Skin Turgor: ' + st.Name + '; ' ELSE '' END,
            CASE WHEN ke.ExpressedMilkByBottlePerDay IS NOT NULL THEN 'Expressed Milk: ' + ke.ExpressedMilkByBottlePerDay + '; ' ELSE '' END,
            CASE WHEN ke.ExpressionOutputPerDay IS NOT NULL THEN 'Expression Output: ' + ke.ExpressionOutputPerDay + '; ' ELSE '' END,
            CASE WHEN ke.FormulaPerDay IS NOT NULL THEN 'Formula: ' + ke.FormulaPerDay + '; ' ELSE '' END,
            CASE WHEN ke.OtherComments IS NOT NULL THEN 'Comments: ' + ke.OtherComments ELSE '' END
        ) AS NVARCHAR(MAX)), ' | ') AS KidEvaluationDetails,

        -- Parent Evaluation Data
        STRING_AGG(CAST(CONCAT(
            CASE WHEN fs.Name IS NOT NULL THEN 'Flange Size: ' + fs.Name + '; ' ELSE '' END,
            CASE WHEN pe.FlangeSizeOtherText IS NOT NULL THEN 'Flange Other: ' + pe.FlangeSizeOtherText + '; ' ELSE '' END,
            CASE WHEN nss.Name IS NOT NULL THEN 'Nipple Shield: ' + nss.Name + '; ' ELSE '' END,
            CASE WHEN pe.NippleShieldSizeOtherText IS NOT NULL THEN 'Shield Other: ' + pe.NippleShieldSizeOtherText + '; ' ELSE '' END,
            CASE WHEN pe.OtherComments IS NOT NULL THEN 'Parent Comments: ' + pe.OtherComments ELSE '' END
        ) AS NVARCHAR(MAX)), ' | ') AS ParentEvaluationDetails,

        -- Lactation Evaluation Types
        STRING_AGG(CAST(CONCAT(
            let.Name,
            CASE WHEN le.OtherText IS NOT NULL AND le.OtherText != '' THEN ' (' + le.OtherText + ')' ELSE '' END
        ) AS NVARCHAR(MAX)), '; ') AS LactationEvaluationTypes

    FROM [dbo].[Evaluations] e
        LEFT JOIN [dbo].[KidEvaluations] ke ON e.Id = ke.Id
        LEFT JOIN [dbo].[SkinTurgor] st ON ke.SkinTurgorId = st.Id
        LEFT JOIN [dbo].[ParentEvaluations] pe ON e.Id = pe.Id
        LEFT JOIN [dbo].[FlangeSizes] fs ON pe.FlangeSizeId = fs.Id
        LEFT JOIN [dbo].[NippleShieldSizes] nss ON pe.NippleShieldSizeId = nss.Id
        LEFT JOIN [dbo].[LactationEvaluations] le ON ke.Id = le.EvaluationId
        LEFT JOIN [dbo].[LactationEvaluationTypes] let ON le.EvaluationTypeId = let.Id
    GROUP BY e.VisitId
),

-- Evaluation Observations Aggregated
EvaluationObservationsAgg AS (
    SELECT
        e.VisitId,

        -- Physical Observations
        STRING_AGG(CAST(CONCAT(
            CASE WHEN app.Name IS NOT NULL THEN 'Appearance: ' + app.Name + '; ' ELSE '' END,
            CASE WHEN bt.Name IS NOT NULL THEN 'Breast: ' + bt.Name + '; ' ELSE '' END,
            CASE WHEN cst.Name IS NOT NULL THEN 'Child State: ' + cst.Name + '; ' ELSE '' END,
            CASE WHEN lt.Name IS NOT NULL THEN 'Lip: ' + lt.Name + '; ' ELSE '' END,
            CASE WHEN nt.Name IS NOT NULL THEN 'Nipple: ' + nt.Name + '; ' ELSE '' END,
            CASE WHEN pt.Name IS NOT NULL THEN 'Palate: ' + pt.Name + '; ' ELSE '' END,
            CASE WHEN sct.Name IS NOT NULL THEN 'Stool Color: ' + sct.Name + '; ' ELSE '' END,
            CASE WHEN scont.Name IS NOT NULL THEN 'Stool Consistency: ' + scont.Name + '; ' ELSE '' END,
            CASE WHEN sut.Name IS NOT NULL THEN 'Suck: ' + sut.Name + '; ' ELSE '' END,
            CASE WHEN tt.Name IS NOT NULL THEN 'Tongue: ' + tt.Name + '; ' ELSE '' END
        ) AS NVARCHAR(MAX)), ' | ') AS PhysicalObservations,

        -- Feeding Methods and Positions
        STRING_AGG(CAST(CONCAT(
            CASE WHEN fmt.Name IS NOT NULL THEN 'Feeding Method: ' + fmt.Name + '; ' ELSE '' END,
            CASE WHEN fpt.Name IS NOT NULL THEN 'Feeding Position: ' + fpt.Name + '; ' ELSE '' END
        ) AS NVARCHAR(MAX)), ' | ') AS FeedingDetails,

        -- Goals and Instructions
        STRING_AGG(CAST(CONCAT(
            CASE WHEN gt.Name IS NOT NULL THEN 'Goal: ' + gt.Name + '; ' ELSE '' END,
            CASE WHEN it.Name IS NOT NULL THEN 'Instruction: ' + it.Name + '; ' ELSE '' END
        ) AS NVARCHAR(MAX)), ' | ') AS GoalsAndInstructions

    FROM [dbo].[Evaluations] e
        LEFT JOIN [dbo].[KidEvaluations] ke ON e.Id = ke.Id

        -- Physical observations
        LEFT JOIN [dbo].[Appearances] a ON ke.Id = a.EvaluationId
        LEFT JOIN [dbo].[AppearanceTypes] app ON a.AppearanceTypeId = app.Id
        LEFT JOIN [dbo].[BreastObservations] bo ON ke.Id = bo.EvaluationId
        LEFT JOIN [dbo].[BreastTypes] bt ON bo.BreastTypeId = bt.Id
        LEFT JOIN [dbo].[ChildStates] cs ON ke.Id = cs.EvaluationId
        LEFT JOIN [dbo].[ChildStateTypes] cst ON cs.ChildStateTypeId = cst.Id
        LEFT JOIN [dbo].[LipObservations] lo ON ke.Id = lo.EvaluationId
        LEFT JOIN [dbo].[LipTypes] lt ON lo.LipTypeId = lt.Id
        LEFT JOIN [dbo].[NippleObservations] no ON ke.Id = no.EvaluationId
        LEFT JOIN [dbo].[NippleTypes] nt ON no.NippleTypeId = nt.Id
        LEFT JOIN [dbo].[PalateObservations] po ON ke.Id = po.EvaluationId
        LEFT JOIN [dbo].[PalateTypes] pt ON po.PalateTypeId = pt.Id
        LEFT JOIN [dbo].[StoolColors] sc ON ke.Id = sc.EvaluationId
        LEFT JOIN [dbo].[StoolColorTypes] sct ON sc.StoolColorTypeId = sct.Id
        LEFT JOIN [dbo].[StoolConsistencies] scon ON ke.Id = scon.EvaluationId
        LEFT JOIN [dbo].[StoolConsistencyTypes] scont ON scon.StoolConsistencyTypeId = scont.Id
        LEFT JOIN [dbo].[Sucks] s ON ke.Id = s.EvaluationId
        LEFT JOIN [dbo].[SuckTypes] sut ON s.SuckTypeId = sut.Id
        LEFT JOIN [dbo].[TongueObservations] to ON ke.Id = to.EvaluationId
        LEFT JOIN [dbo].[TongueTypes] tt ON to.TongueTypeId = tt.Id

        -- Feeding details
        LEFT JOIN [dbo].[FeedingMethods] fm ON ke.Id = fm.EvaluationId
        LEFT JOIN [dbo].[FeedingMethodTypes] fmt ON fm.FeedingMethodTypeId = fmt.Id
        LEFT JOIN [dbo].[FeedingPositions] fp ON ke.Id = fp.EvaluationId
        LEFT JOIN [dbo].[FeedingPositionTypes] fpt ON fp.FeedingPositionTypeId = fpt.Id

        -- Goals and instructions (these link to CarePlans, not KidEvaluations)
        LEFT JOIN [dbo].[Goals] g ON e.Id = g.EvaluationId
        LEFT JOIN [dbo].[GoalTypes] gt ON g.GoalTypeId = gt.Id
        LEFT JOIN [dbo].[Instructions] i ON e.Id = i.EvaluationId
        LEFT JOIN [dbo].[InstructionTypes] it ON i.InstructionTypeId = it.Id

    GROUP BY e.VisitId
),

-- Birth History data aggregated by visit (through Kids/Children)
BirthHistoryAgg AS (
    SELECT
        v.Id AS VisitId,
        COUNT(DISTINCT bh.Id) AS BirthHistoryCount,
        STRING_AGG(CAST(CONCAT(
            'Child: ', child_names.First, ' ', child_names.Last,
            CASE WHEN bh.ApgarOneMinuteScore IS NOT NULL THEN ', Apgar 1min: ' + CAST(bh.ApgarOneMinuteScore AS VARCHAR) ELSE '' END,
            CASE WHEN bh.ApgarFiveMinuteScore IS NOT NULL THEN ', Apgar 5min: ' + CAST(bh.ApgarFiveMinuteScore AS VARCHAR) ELSE '' END,
            CASE WHEN bh.NumWeeksPregnancyAtBirth IS NOT NULL THEN ', Weeks: ' + CAST(bh.NumWeeksPregnancyAtBirth AS VARCHAR) ELSE '' END,
            CASE WHEN dt.Name IS NOT NULL THEN ', Delivery: ' + dt.Name ELSE '' END,
            CASE WHEN bl.Name IS NOT NULL THEN ', Location: ' + bl.Name ELSE '' END
        ) AS NVARCHAR(MAX)), '; ') AS BirthHistoryDetails
    FROM [dbo].[Visits] v
        INNER JOIN [dbo].[Kids] k ON v.ParentId = k.ParentId
        LEFT JOIN [dbo].[BirthHistories] bh ON k.Id = bh.Id
        LEFT JOIN [dbo].[Persons] child_persons ON k.ChildId = child_persons.Id
        LEFT JOIN [dbo].[Names] child_names ON child_persons.Id = child_names.Id
        LEFT JOIN [dbo].[DeliveryTypes] dt ON bh.DeliveryTypeId = dt.Id
        LEFT JOIN [dbo].[BirthLocations] bl ON bh.BirthLocationId = bl.Id
    WHERE bh.Id IS NOT NULL
    GROUP BY v.Id
),

-- Statements data (1:1 with visits)
StatementsData AS (
    SELECT
        s.Id AS VisitId,
        s.IsRetrningToWork,
        s.IsWorkSafeToPump,
        s.Employment,
        s.NumWeeksUntilReturningToWork,
        s.ShortTermBreastfeedingGoals,
        s.LongTermBreastfeedingGoals,
        s.OtherNotes AS StatementOtherNotes
    FROM [dbo].[Statements] s
),

-- Diagnoses aggregated by visit
DiagnosesAgg AS (
    SELECT
        d.VisitId,
        COUNT(*) AS DiagnosesCount,
        STRING_AGG(CAST(CONCAT(
            dt.Name,
            CASE WHEN hs.Name IS NOT NULL THEN ' (' + hs.Name + ')' ELSE '' END,
            CASE WHEN d.OtherText IS NOT NULL AND d.OtherText != '' THEN ' - ' + d.OtherText ELSE '' END
        ) AS NVARCHAR(MAX)), '; ') AS AllDiagnoses
    FROM [dbo].[Diagnoses] d
        LEFT JOIN [dbo].[DiagnosisTypes] dt ON d.DiagnosisTypeId = dt.Id
        LEFT JOIN [dbo].[HumanSystems] hs ON d.HumanSystemId = hs.Id
    GROUP BY d.VisitId
),

-- ICD10 codes aggregated by visit
ICD10Agg AS (
    SELECT
        i.VisitId,
        COUNT(*) AS ICD10Count,
        STRING_AGG(CAST(CONCAT(
            ic.Code, ' - ', ic.Name,
            CASE WHEN i.OtherText IS NOT NULL AND i.OtherText != '' THEN ' (' + i.OtherText + ')' ELSE '' END
        ) AS NVARCHAR(MAX)), '; ') AS AllICD10Codes
    FROM [dbo].[ICD10s] i
        LEFT JOIN [dbo].[ICD10Codes] ic ON i.ICD10CodeId = ic.Id
    GROUP BY i.VisitId
),

-- Care Plans data (through evaluations)
CarePlansAgg AS (
    SELECT
        e.VisitId,
        COUNT(DISTINCT cp.Id) AS CarePlansCount,
        STRING_AGG(CAST(CONCAT(
            CASE WHEN cp.AdditionalGoals IS NOT NULL AND cp.AdditionalGoals != '' THEN 'Goals: ' + cp.AdditionalGoals ELSE '' END,
            CASE WHEN cp.AdditionalInstructions IS NOT NULL AND cp.AdditionalInstructions != '' THEN
                CASE WHEN cp.AdditionalGoals IS NOT NULL AND cp.AdditionalGoals != '' THEN '; ' ELSE '' END + 'Instructions: ' + cp.AdditionalInstructions
            ELSE '' END
        ) AS NVARCHAR(MAX)), '; ') AS CarePlanDetails
    FROM [dbo].[Evaluations] e
        LEFT JOIN [dbo].[CarePlans] cp ON e.Id = cp.Id
    WHERE cp.Id IS NOT NULL
    GROUP BY e.VisitId
)

-- Final result combining all data
SELECT
    vd.*,

    -- Aggregated notes and concerns
    ISNULL(vna.AllVisitNotes, '') AS AllVisitNotes,
    ISNULL(vna.VisitNotesCount, 0) AS VisitNotesCount,

    ISNULL(cna.AllChildNotes, '') AS AllChildNotes,
    ISNULL(cna.ChildNotesCount, 0) AS ChildNotesCount,

    ISNULL(cca.AllChildConcerns, '') AS AllChildConcerns,
    ISNULL(cca.ChildConcernsCount, 0) AS ChildConcernsCount,

    ISNULL(ea.EvaluationsCount, 0) AS EvaluationsCount,
    ISNULL(ea.KidEvaluationDetails, '') AS KidEvaluationDetails,
    ISNULL(ea.ParentEvaluationDetails, '') AS ParentEvaluationDetails,
    ISNULL(ea.LactationEvaluationTypes, '') AS LactationEvaluationTypes,

    -- Evaluation Observations
    ISNULL(eoa.PhysicalObservations, '') AS PhysicalObservations,
    ISNULL(eoa.FeedingDetails, '') AS FeedingDetails,
    ISNULL(eoa.GoalsAndInstructions, '') AS GoalsAndInstructions,

    -- Birth History information
    ISNULL(bha.BirthHistoryCount, 0) AS BirthHistoryCount,
    ISNULL(bha.BirthHistoryDetails, '') AS BirthHistoryDetails,

    -- Statement information
    sd.IsRetrningToWork,
    sd.IsWorkSafeToPump,
    sd.Employment,
    sd.NumWeeksUntilReturningToWork,
    sd.ShortTermBreastfeedingGoals,
    sd.LongTermBreastfeedingGoals,
    sd.StatementOtherNotes,

    -- Diagnoses information
    ISNULL(da.DiagnosesCount, 0) AS DiagnosesCount,
    ISNULL(da.AllDiagnoses, '') AS AllDiagnoses,

    -- ICD10 codes information
    ISNULL(ia.ICD10Count, 0) AS ICD10Count,
    ISNULL(ia.AllICD10Codes, '') AS AllICD10Codes,

    -- Care Plans information
    ISNULL(cpa.CarePlansCount, 0) AS CarePlansCount,
    ISNULL(cpa.CarePlanDetails, '') AS CarePlanDetails

FROM VisitData vd
    LEFT JOIN VisitNotesAgg vna ON vd.VisitId = vna.VisitId
    LEFT JOIN ChildNotesAgg cna ON vd.VisitId = cna.VisitId
    LEFT JOIN ChildConcernsAgg cca ON vd.VisitId = cca.VisitId
    LEFT JOIN EvaluationsAgg ea ON vd.VisitId = ea.VisitId
    LEFT JOIN EvaluationObservationsAgg eoa ON vd.VisitId = eoa.VisitId
    LEFT JOIN BirthHistoryAgg bha ON vd.VisitId = bha.VisitId
    LEFT JOIN StatementsData sd ON vd.VisitId = sd.VisitId
    LEFT JOIN DiagnosesAgg da ON vd.VisitId = da.VisitId
    LEFT JOIN ICD10Agg ia ON vd.VisitId = ia.VisitId
    LEFT JOIN CarePlansAgg cpa ON vd.VisitId = cpa.VisitId

ORDER BY vd.StartDateTime DESC;
