-- Query to get all visit data with detailed child information, notes, and evaluations
-- This query provides a comprehensive view including child concerns, notes, and evaluation data

USE [MilkNotes_local]
GO

-- Main visit data with basic information
WITH VisitData AS (
    SELECT 
        -- Visit basic information
        v.Id AS VisitId,
        v.StartDateTime,
        v.EndDateTime,
        v.ClosedDateTime,
        v.CreateDate AS VisitCreateDate,
        v.EditDate AS VisitEditDate,
        v.EditReason,
        v.DistanceTraveled,
        v.<PERSON>,
        v.PostBuffer,
        v.PreBuffer,
        
        -- Visit status and type information
        vt.Name AS VisitType,
        vs.Name AS VisitStatus,
        vas.Name AS VisitAcceptanceStatus,
        v.VisitNotAcceptedReason,
        v.Is<PERSON>ancelled,
        vcr.Name AS VisitCancellationReason,
        v.VisitCancellationOtherReason,
        v.IsIntakeComplete,
        
        -- Payment information
        v.IsHsaFsa,
        v.IsInsurance,
        v.<PERSON>,
        v.<PERSON><PERSON>,
        spt.Name AS SelfPayType,
        
        -- HIPAA and communication
        v.HasAgreedToHipaaTerms,
        v.<PERSON>o<PERSON>ipaaConsent,
        ct.Name AS PreferredCommunicationType,
        
        -- Parent information
        parent_names.First AS ParentFirstName,
        parent_names.Middle AS ParentMiddleName,
        parent_names.Last AS ParentLastName,
        parent_persons.BirthDate AS ParentBirthDate,
        parent_genders.Name AS ParentGender,
        v.ParentHistoryNotes,
        v.ParentProfileNotes,
        v.ParentConcerns,
        
        -- Consultant information
        consultant_names.First AS ConsultantFirstName,
        consultant_names.Middle AS ConsultantMiddleName,
        consultant_names.Last AS ConsultantLastName,
        
        -- Appointment type information
        at.Name AS AppointmentType,
        at.Description AS AppointmentDescription,
        at.DefaultDuration AS AppointmentDefaultDuration,
        cl.Name AS ConsultLocation,
        
        -- Visit notes
        v.MedicalReportNotes,
        
        -- Subscriber information
        s.Id AS SubscriberId

    FROM [dbo].[Visits] v
        
        -- Join with visit lookup tables
        LEFT JOIN [dbo].[VisitTypes] vt ON v.VisitTypeId = vt.Id
        LEFT JOIN [dbo].[VisitStatuses] vs ON v.VisitStatusId = vs.Id
        LEFT JOIN [dbo].[VisitAcceptanceStatuses] vas ON v.VisitAcceptanceStatusId = vas.Id
        LEFT JOIN [dbo].[VisitCancellationReasons] vcr ON v.VisitCancellationReasonId = vcr.Id
        LEFT JOIN [dbo].[SelfPayTypes] spt ON v.SelfPayTypeId = spt.Id
        LEFT JOIN [dbo].[CommunicationTypes] ct ON v.PreferredCommunicationTypeId = ct.Id
        
        -- Join with parent information
        LEFT JOIN [dbo].[Persons] parent_persons ON v.ParentId = parent_persons.Id
        LEFT JOIN [dbo].[Names] parent_names ON parent_persons.Id = parent_names.Id
        LEFT JOIN [dbo].[Genders] parent_genders ON parent_persons.GenderId = parent_genders.Id
        
        -- Join with consultant information
        LEFT JOIN [dbo].[Persons] consultant_persons ON v.ConsultantId = consultant_persons.Id
        LEFT JOIN [dbo].[Names] consultant_names ON consultant_persons.Id = consultant_names.Id
        
        -- Join with appointment type and location
        LEFT JOIN [dbo].[AppointmentTypes] at ON v.AppointmentTypeId = at.Id
        LEFT JOIN [dbo].[ConsultLocations] cl ON at.ConsultLocationId = cl.Id
        
        -- Join with subscriber
        LEFT JOIN [dbo].[Subscribers] s ON v.SubscriberId = s.Id
),

-- Visit Notes aggregated
VisitNotesAgg AS (
    SELECT 
        vn.VisitId,
        STRING_AGG(CAST(vn.Note AS NVARCHAR(MAX)), '; ') AS AllVisitNotes,
        COUNT(*) AS VisitNotesCount
    FROM [dbo].[VisitNotes] vn
    GROUP BY vn.VisitId
),

-- Child Notes aggregated
ChildNotesAgg AS (
    SELECT 
        vcn.VisitId,
        STRING_AGG(CAST(vcn.Note AS NVARCHAR(MAX)), '; ') AS AllChildNotes,
        COUNT(*) AS ChildNotesCount
    FROM [dbo].[VisitChildNotes] vcn
    GROUP BY vcn.VisitId
),

-- Child Concerns aggregated
ChildConcernsAgg AS (
    SELECT 
        vcc.VisitId,
        STRING_AGG(CAST(cc.Name AS NVARCHAR(MAX)), '; ') AS AllChildConcerns,
        COUNT(*) AS ChildConcernsCount
    FROM [dbo].[VisitChildConcerns] vcc
    LEFT JOIN [dbo].[ChildConcerns] cc ON vcc.ChildConcernId = cc.Id
    GROUP BY vcc.VisitId
),

-- Evaluations count
EvaluationsAgg AS (
    SELECT 
        e.VisitId,
        COUNT(*) AS EvaluationsCount
    FROM [dbo].[Evaluations] e
    GROUP BY e.VisitId
)

-- Final result combining all data
SELECT 
    vd.*,
    
    -- Aggregated notes and concerns
    ISNULL(vna.AllVisitNotes, '') AS AllVisitNotes,
    ISNULL(vna.VisitNotesCount, 0) AS VisitNotesCount,
    
    ISNULL(cna.AllChildNotes, '') AS AllChildNotes,
    ISNULL(cna.ChildNotesCount, 0) AS ChildNotesCount,
    
    ISNULL(cca.AllChildConcerns, '') AS AllChildConcerns,
    ISNULL(cca.ChildConcernsCount, 0) AS ChildConcernsCount,
    
    ISNULL(ea.EvaluationsCount, 0) AS EvaluationsCount

FROM VisitData vd
    LEFT JOIN VisitNotesAgg vna ON vd.VisitId = vna.VisitId
    LEFT JOIN ChildNotesAgg cna ON vd.VisitId = cna.VisitId
    LEFT JOIN ChildConcernsAgg cca ON vd.VisitId = cca.VisitId
    LEFT JOIN EvaluationsAgg ea ON vd.VisitId = ea.VisitId

ORDER BY vd.StartDateTime DESC;
