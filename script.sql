USE [MilkNotes_local]
GO

-- Query to get all visit data with related information
SELECT
    -- Visit basic information
    v.Id AS VisitId,
    v.StartDateTime,
    v.EndDateTime,
    v.ClosedDateTime,
    v.CreateDate AS VisitCreateDate,
    v.EditDate AS VisitEditDate,
    v.EditReason,
    v.<PERSON>Traveled,
    v.<PERSON>,
    v.PostBuffer,
    v.<PERSON>Buffer,

    -- Visit status and type information
    vt.Name AS VisitType,
    vs.Name AS VisitStatus,
    vas.Name AS VisitAcceptanceStatus,
    v.VisitNotAcceptedReason,
    v.IsCancelled,
    vcr.Name AS VisitCancellationReason,
    v.VisitCancellationOtherReason,
    v.Is<PERSON>ntakeComplete,

    -- Payment information
    v.IsHsaFsa,
    v.IsInsurance,
    v.IsMedicaid,
    v.IsSelfPay,
    spt.Name AS SelfPayType,

    -- HIPAA and communication
    v.HasAg<PERSON><PERSON><PERSON><PERSON><PERSON>aa<PERSON><PERSON><PERSON>,
    v.<PERSON>g<PERSON><PERSON><PERSON><PERSON>aa<PERSON><PERSON><PERSON>,
    ct.Name AS PreferredCommunicationType,

    -- Parent information
    parent_names.First AS ParentFirstName,
    parent_names.Middle AS ParentMiddleName,
    parent_names.Last AS ParentLastName,
    parent_persons.BirthDate AS ParentBirthDate,
    parent_genders.Name AS ParentGender,
    v.ParentHistoryNotes,
    v.ParentProfileNotes,
    v.ParentConcerns,

    -- Consultant information
    consultant_names.First AS ConsultantFirstName,
    consultant_names.Middle AS ConsultantMiddleName,
    consultant_names.Last AS ConsultantLastName,

    -- Appointment type information
    at.Name AS AppointmentType,
    at.Description AS AppointmentDescription,
    at.DefaultDuration AS AppointmentDefaultDuration,
    cl.Name AS ConsultLocation,

    -- Visit notes
    v.MedicalReportNotes,

    -- Subscriber information
    s.Id AS SubscriberId

FROM [dbo].[Visits] v

    -- Join with visit lookup tables
    LEFT JOIN [dbo].[VisitTypes] vt ON v.VisitTypeId = vt.Id
    LEFT JOIN [dbo].[VisitStatuses] vs ON v.VisitStatusId = vs.Id
    LEFT JOIN [dbo].[VisitAcceptanceStatuses] vas ON v.VisitAcceptanceStatusId = vas.Id
    LEFT JOIN [dbo].[VisitCancellationReasons] vcr ON v.VisitCancellationReasonId = vcr.Id
    LEFT JOIN [dbo].[SelfPayTypes] spt ON v.SelfPayTypeId = spt.Id
    LEFT JOIN [dbo].[CommunicationTypes] ct ON v.PreferredCommunicationTypeId = ct.Id

    -- Join with parent information
    LEFT JOIN [dbo].[Persons] parent_persons ON v.ParentId = parent_persons.Id
    LEFT JOIN [dbo].[Names] parent_names ON parent_persons.Id = parent_names.Id
    LEFT JOIN [dbo].[Genders] parent_genders ON parent_persons.GenderId = parent_genders.Id

    -- Join with consultant information
    LEFT JOIN [dbo].[Persons] consultant_persons ON v.ConsultantId = consultant_persons.Id
    LEFT JOIN [dbo].[Names] consultant_names ON consultant_persons.Id = consultant_names.Id

    -- Join with appointment type and location
    LEFT JOIN [dbo].[AppointmentTypes] at ON v.AppointmentTypeId = at.Id
    LEFT JOIN [dbo].[ConsultLocations] cl ON at.ConsultLocationId = cl.Id

    -- Join with subscriber
    LEFT JOIN [dbo].[Subscribers] s ON v.SubscriberId = s.Id

ORDER BY v.StartDateTime DESC;

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Addresses](
	[Id] [uniqueidentifier] NOT NULL,
	[City] [nvarchar](256) NULL,
	[Country] [nvarchar](256) NULL,
	[Line1] [nvarchar](256) NULL,
	[Line2] [nvarchar](256) NULL,
	[Line3] [nvarchar](256) NULL,
	[PostalCode] [nvarchar](25) NULL,
	[Region] [nvarchar](256) NULL,
	[AddressTypeId] [int] NOT NULL,
 CONSTRAINT [PK_Addresses] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[AddressTypes](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_AddressTypes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[AppearanceCustomOptions](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[EditDate] [datetime2](7) NULL,
	[SubscriberId] [uniqueidentifier] NOT NULL,
	[Text] [nvarchar](max) NULL,
	[IsDeleted] [bit] NOT NULL,
	[IsParentEntered] [bit] NOT NULL,
 CONSTRAINT [PK_AppearanceCustomOptions] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Appearances](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[EvaluationId] [uniqueidentifier] NOT NULL,
	[AppearanceTypeId] [int] NOT NULL,
	[OtherText] [nvarchar](100) NULL,
	[AppearanceCustomOptionId] [uniqueidentifier] NULL,
	[AntiDuplicationIndex] [nvarchar](200) NOT NULL,
 CONSTRAINT [PK_Appearances] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[AppearanceTypes](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_AppearanceTypes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[AppointmentTypes](
	[Id] [uniqueidentifier] NOT NULL,
	[Color] [nvarchar](7) NULL,
	[ConsultLocationId] [int] NOT NULL,
	[DefaultDuration] [int] NOT NULL,
	[Description] [nvarchar](max) NULL,
	[IsPriceShownOnPublicCalendar] [bit] NOT NULL,
	[IsPublicFacing] [bit] NOT NULL,
	[Name] [nvarchar](100) NULL,
	[PostBuffer] [int] NOT NULL,
	[PreBuffer] [int] NOT NULL,
	[Price] [int] NULL,
	[SubscriberId] [uniqueidentifier] NOT NULL,
	[IsDurationShownOnPublicCalendar] [bit] NOT NULL,
	[BlackoutDurationMinutes] [int] NOT NULL,
 CONSTRAINT [PK_AppointmentTypes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[BirthHistories](
	[Id] [uniqueidentifier] NOT NULL,
	[BirthLocationId] [int] NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[DeliveryTypeId] [int] NULL,
	[EditDate] [datetime2](7) NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[OtherComments] [nvarchar](max) NULL,
	[ParentComments] [nvarchar](max) NULL,
	[ApgarFiveMinuteScore] [int] NULL,
	[ApgarOneMinuteScore] [int] NULL,
	[WasChildResuscitated] [bit] NOT NULL,
	[CesareanReasonId] [int] NULL,
	[BilirubenLevels] [decimal](18, 2) NULL,
	[NumDaysPregnancyAtBirth] [int] NULL,
	[NumWeeksPregnancyAtBirth] [int] NULL,
	[GrowthCategoryId] [int] NULL,
	[BirthLocationNameId] [uniqueidentifier] NULL,
 CONSTRAINT [PK_BirthHistories] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[BirthLocationNameCustomOptions](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[EditDate] [datetime2](7) NULL,
	[SubscriberId] [uniqueidentifier] NOT NULL,
	[Text] [nvarchar](max) NULL,
	[IsDeleted] [bit] NOT NULL,
	[IsParentEntered] [bit] NOT NULL,
 CONSTRAINT [PK_BirthLocationNameCustomOptions] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[BirthLocations](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_BirthLocations] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[BreastCustomOptions](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[EditDate] [datetime2](7) NULL,
	[SubscriberId] [uniqueidentifier] NOT NULL,
	[Text] [nvarchar](max) NULL,
	[IsDeleted] [bit] NOT NULL,
	[IsParentEntered] [bit] NOT NULL,
 CONSTRAINT [PK_BreastCustomOptions] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[BreastFeedingHistories](
	[Id] [uniqueidentifier] NOT NULL,
	[AverageMonths] [int] NULL,
	[ChildrenCount] [int] NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[EditDate] [datetime2](7) NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[OtherComments] [nvarchar](max) NULL,
	[ParentId] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_BreastFeedingHistories] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[BreastObservations](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[EvaluationId] [uniqueidentifier] NOT NULL,
	[BreastTypeId] [int] NOT NULL,
	[OtherText] [nvarchar](100) NULL,
	[BreastCustomOptionId] [uniqueidentifier] NULL,
	[AntiDuplicationIndex] [nvarchar](200) NOT NULL,
 CONSTRAINT [PK_BreastObservations] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[BreastPumpRentals](
	[Id] [uniqueidentifier] NOT NULL,
	[IsPaidFor] [bit] NOT NULL,
	[IssuedById] [uniqueidentifier] NOT NULL,
	[IssuedToId] [uniqueidentifier] NOT NULL,
	[PaymentType] [nvarchar](max) NULL,
	[PumpConditionWhenRented] [nvarchar](max) NULL,
	[PumpConditionWhenReturned] [nvarchar](max) NULL,
	[RentalFee] [decimal](18, 2) NOT NULL,
	[RentalReturnDate] [datetime2](7) NULL,
	[RentalStartDate] [datetime2](7) NOT NULL,
	[SubscriberBreastPumpId] [uniqueidentifier] NOT NULL,
	[DueDate] [datetime2](7) NOT NULL,
	[Notes] [nvarchar](max) NULL,
 CONSTRAINT [PK_BreastPumpRentals] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[BreastPumps](
	[Id] [uniqueidentifier] NOT NULL,
	[PumpBrandId] [int] NULL,
	[PumpTypeId] [int] NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[OtherBrandName] [nvarchar](250) NULL,
	[PumpBrandCustomOptionId] [uniqueidentifier] NULL,
 CONSTRAINT [PK_BreastPumps] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[BreastPumpServiceHistories](
	[Id] [uniqueidentifier] NOT NULL,
	[PumpReturnedFromServiceDate] [datetime2](7) NULL,
	[PumpSentForServiceDate] [datetime2](7) NOT NULL,
	[PumpServiceNotes] [nvarchar](max) NULL,
	[SubscriberBreastPumpId] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_BreastPumpServiceHistories] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[BreastTypes](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_BreastTypes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Calendars](
	[Id] [uniqueidentifier] NOT NULL,
	[CompanyName] [nvarchar](500) NULL,
	[Details] [nvarchar](max) NULL,
	[ExternalCalendarId] [int] IDENTITY(1,1) NOT NULL,
	[ImageId] [uniqueidentifier] NULL,
	[IsPublic] [bit] NOT NULL,
	[LogoImageId] [uniqueidentifier] NULL,
 CONSTRAINT [PK_Calendars] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[CarePlanCustomInstructions](
	[Id] [uniqueidentifier] NOT NULL,
	[CarePlanId] [uniqueidentifier] NOT NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[CustomInstructionId] [uniqueidentifier] NOT NULL,
	[EditDate] [datetime2](7) NULL,
	[EditUserId] [uniqueidentifier] NULL,
 CONSTRAINT [PK_CarePlanCustomInstructions] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[CarePlanDocumentResources](
	[Id] [uniqueidentifier] NOT NULL,
	[CarePlanId] [uniqueidentifier] NOT NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[DocumentResourceId] [uniqueidentifier] NOT NULL,
	[EditDate] [datetime2](7) NULL,
	[EditUserId] [uniqueidentifier] NULL,
 CONSTRAINT [PK_CarePlanDocumentResources] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[CarePlanReferralProviders](
	[Id] [uniqueidentifier] NOT NULL,
	[CarePlanId] [uniqueidentifier] NOT NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[EditDate] [datetime2](7) NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[SubscriberProviderId] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_CarePlanReferralProviders] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[CarePlans](
	[Id] [uniqueidentifier] NOT NULL,
	[AdditionalGoals] [nvarchar](max) NULL,
	[AdditionalInstructions] [nvarchar](max) NULL,
 CONSTRAINT [PK_CarePlans] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[CesareanReasons](
	[Id] [int] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_CesareanReasons] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ChildConcerns](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_ChildConcerns] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ChildProfileNotes](
	[Id] [uniqueidentifier] NOT NULL,
	[ChildId] [uniqueidentifier] NOT NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[EditDate] [datetime2](7) NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[Note] [nvarchar](max) NULL,
	[VisitId] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_ChildProfileNotes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ChildStateCustomOptions](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[EditDate] [datetime2](7) NULL,
	[SubscriberId] [uniqueidentifier] NOT NULL,
	[Text] [nvarchar](max) NULL,
	[IsDeleted] [bit] NOT NULL,
	[IsParentEntered] [bit] NOT NULL,
 CONSTRAINT [PK_ChildStateCustomOptions] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ChildStates](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[EvaluationId] [uniqueidentifier] NOT NULL,
	[ChildStateTypeId] [int] NOT NULL,
	[OtherText] [nvarchar](100) NULL,
	[ChildStateCustomOptionId] [uniqueidentifier] NULL,
	[AntiDuplicationIndex] [nvarchar](200) NOT NULL,
 CONSTRAINT [PK_ChildStates] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ChildStateTypes](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_ChildStateTypes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[CommunicationTypes](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_CommunicationTypes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ComplicationCustomOptions](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[EditDate] [datetime2](7) NULL,
	[SubscriberId] [uniqueidentifier] NOT NULL,
	[Text] [nvarchar](max) NULL,
	[IsDeleted] [bit] NOT NULL,
	[IsParentEntered] [bit] NOT NULL,
 CONSTRAINT [PK_ComplicationCustomOptions] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Complications](
	[Id] [uniqueidentifier] NOT NULL,
	[BirthHistoryId] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[ComplicationTypeId] [int] NOT NULL,
	[OtherText] [nvarchar](100) NULL,
	[ComplicationCustomOptionId] [uniqueidentifier] NULL,
	[AntiDuplicationIndex] [nvarchar](200) NOT NULL,
 CONSTRAINT [PK_Complications] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ComplicationTypes](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_ComplicationTypes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ConcernCustomOptions](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[EditDate] [datetime2](7) NULL,
	[SubscriberId] [uniqueidentifier] NOT NULL,
	[Text] [nvarchar](max) NULL,
	[IsDeleted] [bit] NOT NULL,
	[IsParentEntered] [bit] NOT NULL,
 CONSTRAINT [PK_ConcernCustomOptions] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Concerns](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_Concerns] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ConsultLocations](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_ConsultLocations] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[CustomInstructions](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[EditDate] [datetime2](7) NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[GoalTypeId] [int] NOT NULL,
	[InstructionText] [nvarchar](1000) NULL,
	[IsDeleted] [bit] NOT NULL,
	[SortOrder] [int] NOT NULL,
	[SubscriberId] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_CustomInstructions] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[DailyAvailabilities](
	[Id] [uniqueidentifier] NOT NULL,
	[DayOfWeek] [int] NOT NULL,
	[EndTime] [int] NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[IsPublic] [bit] NOT NULL,
	[StartTime] [int] NOT NULL,
	[SubscriberUserId] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_DailyAvailabilities] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[DailyAvailabilityAppointmentTypes](
	[Id] [uniqueidentifier] NOT NULL,
	[AppointmentTypeId] [uniqueidentifier] NOT NULL,
	[DailyAvailabilityId] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_DailyAvailabilityAppointmentTypes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[DeliveryTypes](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_DeliveryTypes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Diagnoses](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[DiagnosisTypeId] [int] NOT NULL,
	[OtherText] [nvarchar](100) NULL,
	[PersonId] [uniqueidentifier] NOT NULL,
	[VisitId] [uniqueidentifier] NOT NULL,
	[HumanSystemId] [int] NOT NULL,
	[DiagnosisTypeCategorizedCustomOptionId] [uniqueidentifier] NULL,
	[AntiDuplicationIndex] [nvarchar](200) NOT NULL,
 CONSTRAINT [PK_Diagnoses] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[DiagnosisTypeCategorizedCustomOptions](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[EditDate] [datetime2](7) NULL,
	[SubscriberId] [uniqueidentifier] NOT NULL,
	[Text] [nvarchar](max) NULL,
	[ParentValueId] [int] NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[IsParentEntered] [bit] NOT NULL,
 CONSTRAINT [PK_DiagnosisTypeCategorizedCustomOptions] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[DiagnosisTypes](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[IsForChild] [bit] NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
	[HumanSystemId] [int] NOT NULL,
 CONSTRAINT [PK_DiagnosisTypes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[DocumentResources](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[EditDate] [datetime2](7) NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](max) NOT NULL,
	[SubscriberId] [uniqueidentifier] NOT NULL,
	[Url] [nvarchar](max) NOT NULL,
	[IsFolder] [bit] NOT NULL,
	[ParentDocumentResourceId] [uniqueidentifier] NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_DocumentResources] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Emails](
	[Id] [uniqueidentifier] NOT NULL,
	[Address] [nvarchar](512) NULL,
	[EmailTypeId] [int] NOT NULL,
 CONSTRAINT [PK_Emails] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[EmailTypes]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[EmailTypes](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_EmailTypes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Evaluations]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Evaluations](
	[Id] [uniqueidentifier] NOT NULL,
	[ChildId] [uniqueidentifier] NULL,
	[VisitId] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_Evaluations] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[FeedingMethodCustomOptions]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[FeedingMethodCustomOptions](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[EditDate] [datetime2](7) NULL,
	[SubscriberId] [uniqueidentifier] NOT NULL,
	[Text] [nvarchar](max) NULL,
	[IsDeleted] [bit] NOT NULL,
	[IsParentEntered] [bit] NOT NULL,
 CONSTRAINT [PK_FeedingMethodCustomOptions] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[FeedingMethods]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[FeedingMethods](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[EvaluationId] [uniqueidentifier] NOT NULL,
	[FeedingMethodTypeId] [int] NOT NULL,
	[OtherText] [nvarchar](100) NULL,
	[FeedingMethodCustomOptionId] [uniqueidentifier] NULL,
	[AntiDuplicationIndex] [nvarchar](200) NOT NULL,
 CONSTRAINT [PK_FeedingMethods] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[FeedingMethodTypes]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[FeedingMethodTypes](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_FeedingMethodTypes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[FeedingPositionCustomOptions]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[FeedingPositionCustomOptions](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[EditDate] [datetime2](7) NULL,
	[SubscriberId] [uniqueidentifier] NOT NULL,
	[Text] [nvarchar](max) NULL,
	[IsDeleted] [bit] NOT NULL,
	[IsParentEntered] [bit] NOT NULL,
 CONSTRAINT [PK_FeedingPositionCustomOptions] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[FeedingPositions]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[FeedingPositions](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[EvaluationId] [uniqueidentifier] NOT NULL,
	[FeedingPositionTypeId] [int] NOT NULL,
	[OtherText] [nvarchar](100) NULL,
	[FeedingPositionCustomOptionId] [uniqueidentifier] NULL,
	[AntiDuplicationIndex] [nvarchar](200) NOT NULL,
 CONSTRAINT [PK_FeedingPositions] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[FeedingPositionTypes]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[FeedingPositionTypes](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_FeedingPositionTypes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[FlangeSizeCustomOptions]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[FlangeSizeCustomOptions](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[EditDate] [datetime2](7) NULL,
	[SubscriberId] [uniqueidentifier] NOT NULL,
	[Text] [nvarchar](max) NULL,
	[IsDeleted] [bit] NOT NULL,
	[IsParentEntered] [bit] NOT NULL,
 CONSTRAINT [PK_FlangeSizeCustomOptions] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[FlangeSizes]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[FlangeSizes](
	[Id] [int] NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_FlangeSizes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Genders]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Genders](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_Genders] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Goals]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Goals](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[EvaluationId] [uniqueidentifier] NOT NULL,
	[GoalTypeId] [int] NOT NULL,
	[OtherText] [nvarchar](100) NULL,
	[CustomOptionId] [uniqueidentifier] NULL,
	[AntiDuplicationIndex] [nvarchar](200) NULL,
 CONSTRAINT [PK_Goals] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[GoalTypes]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[GoalTypes](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_GoalTypes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[GrowthCategories]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[GrowthCategories](
	[Id] [int] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_GrowthCategories] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[HumanSystems]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[HumanSystems](
	[Id] [int] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_HumanSystems] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ICD10CodeCustomOptions]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ICD10CodeCustomOptions](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[EditDate] [datetime2](7) NULL,
	[SubscriberId] [uniqueidentifier] NOT NULL,
	[Text] [nvarchar](max) NULL,
	[IsDeleted] [bit] NOT NULL,
	[IsParentEntered] [bit] NOT NULL,
 CONSTRAINT [PK_ICD10CodeCustomOptions] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ICD10Codes]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ICD10Codes](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[Code] [nvarchar](150) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[IsForChild] [bit] NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_ICD10Codes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ICD10s]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ICD10s](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[ICD10CodeId] [int] NOT NULL,
	[OtherText] [nvarchar](100) NULL,
	[PersonId] [uniqueidentifier] NOT NULL,
	[VisitId] [uniqueidentifier] NOT NULL,
	[ICD10CodeCustomOptionId] [uniqueidentifier] NULL,
	[AntiDuplicationIndex] [nvarchar](200) NOT NULL,
 CONSTRAINT [PK_ICD10s] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Images]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Images](
	[Id] [uniqueidentifier] NOT NULL,
	[Description] [nvarchar](max) NULL,
	[Path] [nvarchar](512) NOT NULL,
	[ContentType] [nvarchar](200) NOT NULL,
	[ThumbnailPath] [nvarchar](512) NOT NULL,
 CONSTRAINT [PK_Images] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Instructions]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Instructions](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[EvaluationId] [uniqueidentifier] NOT NULL,
	[InstructionTypeId] [int] NOT NULL,
	[OtherText] [nvarchar](100) NULL,
	[CustomOptionId] [uniqueidentifier] NULL,
	[AntiDuplicationIndex] [nvarchar](200) NULL,
 CONSTRAINT [PK_Instructions] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[InstructionTypes]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[InstructionTypes](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_InstructionTypes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[InsurancePolicies]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[InsurancePolicies](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[EditDate] [datetime2](7) NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[GroupId] [nvarchar](256) NULL,
	[IsCurrent] [bit] NOT NULL,
	[MemberId] [nvarchar](256) NULL,
	[PersonId] [uniqueidentifier] NOT NULL,
	[PolicyHolderId] [uniqueidentifier] NULL,
	[PolicyInsuranceCompanyId] [uniqueidentifier] NULL,
	[ParentReportedInsuranceCompany] [nvarchar](max) NULL,
	[ParentReportedInsuranceCompanyPhone] [nvarchar](max) NULL,
	[EffectiveDate] [datetimeoffset](7) NULL,
	[TerminationDate] [datetimeoffset](7) NULL,
 CONSTRAINT [PK_InsurancePolicies] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[InsurancePolicyImages]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[InsurancePolicyImages](
	[Id] [uniqueidentifier] NOT NULL,
	[ImageId] [uniqueidentifier] NOT NULL,
	[InsurancePolicyId] [uniqueidentifier] NOT NULL,
	[IsPrimary] [bit] NOT NULL,
	[CreateDate] [datetimeoffset](7) NULL,
	[CreateUserId] [uniqueidentifier] NULL,
 CONSTRAINT [PK_InsurancePolicyImages] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[InterventionCustomOptions]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[InterventionCustomOptions](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[EditDate] [datetime2](7) NULL,
	[SubscriberId] [uniqueidentifier] NOT NULL,
	[Text] [nvarchar](max) NULL,
	[IsDeleted] [bit] NOT NULL,
	[IsParentEntered] [bit] NOT NULL,
 CONSTRAINT [PK_InterventionCustomOptions] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Interventions]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Interventions](
	[Id] [uniqueidentifier] NOT NULL,
	[BirthHistoryId] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[InterventionTypeId] [int] NOT NULL,
	[OtherText] [nvarchar](100) NULL,
	[InterventionCustomOptionId] [uniqueidentifier] NULL,
	[AntiDuplicationIndex] [nvarchar](200) NOT NULL,
 CONSTRAINT [PK_Interventions] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[InterventionTypes]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[InterventionTypes](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[IsForChild] [bit] NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_InterventionTypes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[KidEvaluations]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[KidEvaluations](
	[Id] [uniqueidentifier] NOT NULL,
	[OtherComments] [nvarchar](max) NULL,
	[StoolsPerDay] [int] NULL,
	[VoidsPerDay] [int] NULL,
	[ExpressedMilkByBottlePerDay] [nvarchar](max) NULL,
	[ExpressionOutputPerDay] [nvarchar](max) NULL,
	[FormulaPerDay] [nvarchar](max) NULL,
	[NumExpressionsPerDay] [int] NULL,
	[NumFeedsPerDay] [int] NULL,
	[SkinTurgorId] [int] NULL,
	[FeedingDurationMinutes] [nvarchar](max) NULL,
	[FeedingFrequencyHours] [nvarchar](max) NULL,
 CONSTRAINT [PK_KidEvaluations] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Kids]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Kids](
	[Id] [uniqueidentifier] NOT NULL,
	[BirthOrder] [int] NULL,
	[ChildId] [uniqueidentifier] NOT NULL,
	[ParentId] [uniqueidentifier] NOT NULL,
	[IsDeleted] [bit] NOT NULL,
 CONSTRAINT [PK_Kids] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[LactationEvaluationCustomOptions]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[LactationEvaluationCustomOptions](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[EditDate] [datetime2](7) NULL,
	[SubscriberId] [uniqueidentifier] NOT NULL,
	[Text] [nvarchar](max) NULL,
	[IsDeleted] [bit] NOT NULL,
	[IsParentEntered] [bit] NOT NULL,
 CONSTRAINT [PK_LactationEvaluationCustomOptions] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[LactationEvaluations]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[LactationEvaluations](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[EvaluationId] [uniqueidentifier] NOT NULL,
	[EvaluationTypeId] [int] NOT NULL,
	[OtherText] [nvarchar](100) NULL,
	[LactationEvaluationCustomOptionId] [uniqueidentifier] NULL,
	[AntiDuplicationIndex] [nvarchar](200) NOT NULL,
 CONSTRAINT [PK_LactationEvaluations] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[LactationEvaluationTypes]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[LactationEvaluationTypes](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_LactationEvaluationTypes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[LineTypes]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[LineTypes](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_LineTypes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[LipCustomOptions]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[LipCustomOptions](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[EditDate] [datetime2](7) NULL,
	[SubscriberId] [uniqueidentifier] NOT NULL,
	[Text] [nvarchar](max) NULL,
	[IsDeleted] [bit] NOT NULL,
	[IsParentEntered] [bit] NOT NULL,
 CONSTRAINT [PK_LipCustomOptions] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[LipObservations]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[LipObservations](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[EvaluationId] [uniqueidentifier] NOT NULL,
	[LipTypeId] [int] NOT NULL,
	[OtherText] [nvarchar](100) NULL,
	[LipCustomOptionId] [uniqueidentifier] NULL,
	[AntiDuplicationIndex] [nvarchar](200) NOT NULL,
 CONSTRAINT [PK_LipObservations] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[LipTypes]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[LipTypes](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_LipTypes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[MeasuredWeights]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[MeasuredWeights](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[EditDate] [datetime2](7) NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[PersonId] [uniqueidentifier] NOT NULL,
	[SubValue] [decimal](18, 2) NULL,
	[Value] [decimal](18, 2) NULL,
	[VisitId] [uniqueidentifier] NOT NULL,
	[WeighDate] [datetimeoffset](7) NULL,
	[WeightTypeId] [int] NULL,
	[WeightUnitId] [int] NOT NULL,
 CONSTRAINT [PK_MeasuredWeights] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[MedicationCustomOptions]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[MedicationCustomOptions](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[EditDate] [datetime2](7) NULL,
	[SubscriberId] [uniqueidentifier] NOT NULL,
	[Text] [nvarchar](max) NULL,
	[IsDeleted] [bit] NOT NULL,
	[IsParentEntered] [bit] NOT NULL,
 CONSTRAINT [PK_MedicationCustomOptions] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Medications]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Medications](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[MedicationTypeId] [int] NOT NULL,
	[OtherText] [nvarchar](100) NULL,
	[PersonId] [uniqueidentifier] NOT NULL,
	[VisitId] [uniqueidentifier] NOT NULL,
	[Duration] [nvarchar](max) NULL,
	[IsDoseComplete] [bit] NOT NULL,
	[NumTimesPerDay] [int] NULL,
	[Strength] [nvarchar](max) NULL,
	[MedicationCustomOptionId] [uniqueidentifier] NULL,
	[AntiDuplicationIndex] [nvarchar](200) NULL,
 CONSTRAINT [PK_Medications] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[MedicationTypes]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[MedicationTypes](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsBirthSpecific] [bit] NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[IsForChild] [bit] NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_MedicationTypes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Names]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Names](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[EditDate] [datetime2](7) NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[First] [nvarchar](70) NULL,
	[Last] [nvarchar](70) NULL,
	[Middle] [nvarchar](70) NULL,
	[PrefixId] [int] NULL,
	[SuffixId] [int] NULL,
 CONSTRAINT [PK_Names] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[NippleCustomOptions]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[NippleCustomOptions](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[EditDate] [datetime2](7) NULL,
	[SubscriberId] [uniqueidentifier] NOT NULL,
	[Text] [nvarchar](max) NULL,
	[IsDeleted] [bit] NOT NULL,
	[IsParentEntered] [bit] NOT NULL,
 CONSTRAINT [PK_NippleCustomOptions] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[NippleObservations]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[NippleObservations](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[EvaluationId] [uniqueidentifier] NOT NULL,
	[NippleTypeId] [int] NOT NULL,
	[OtherText] [nvarchar](100) NULL,
	[NippleCustomOptionId] [uniqueidentifier] NULL,
	[AntiDuplicationIndex] [nvarchar](200) NOT NULL,
 CONSTRAINT [PK_NippleObservations] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[NippleShieldCustomOptions]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[NippleShieldCustomOptions](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[EditDate] [datetime2](7) NULL,
	[SubscriberId] [uniqueidentifier] NOT NULL,
	[Text] [nvarchar](max) NULL,
	[IsDeleted] [bit] NOT NULL,
	[IsParentEntered] [bit] NOT NULL,
 CONSTRAINT [PK_NippleShieldCustomOptions] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[NippleShieldSizes]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[NippleShieldSizes](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_NippleShieldSizes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[NippleTypes]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[NippleTypes](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_NippleTypes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[NoteTypes]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[NoteTypes](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_NoteTypes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[PalateCustomOptions]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[PalateCustomOptions](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[EditDate] [datetime2](7) NULL,
	[SubscriberId] [uniqueidentifier] NOT NULL,
	[Text] [nvarchar](max) NULL,
	[IsDeleted] [bit] NOT NULL,
	[IsParentEntered] [bit] NOT NULL,
 CONSTRAINT [PK_PalateCustomOptions] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[PalateObservations]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[PalateObservations](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[EvaluationId] [uniqueidentifier] NOT NULL,
	[PalateTypeId] [int] NOT NULL,
	[OtherText] [nvarchar](100) NULL,
	[PalateCustomOptionId] [uniqueidentifier] NULL,
	[AntiDuplicationIndex] [nvarchar](200) NOT NULL,
 CONSTRAINT [PK_PalateObservations] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[PalateTypes]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[PalateTypes](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_PalateTypes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ParentEvaluations]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ParentEvaluations](
	[Id] [uniqueidentifier] NOT NULL,
	[BreastPumpId] [uniqueidentifier] NULL,
	[NippleShieldSizeId] [int] NULL,
	[OtherComments] [nvarchar](max) NULL,
	[FlangeSizeId] [int] NULL,
	[FlangeSizeOtherText] [nvarchar](250) NULL,
	[NippleShieldSizeOtherText] [nvarchar](250) NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[FlangeSizeCustomOptionId] [uniqueidentifier] NULL,
	[NippleShieldCustomOptionId] [uniqueidentifier] NULL,
 CONSTRAINT [PK_ParentEvaluations] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ParentMedicalHistory]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ParentMedicalHistory](
	[Id] [uniqueidentifier] NOT NULL,
	[AgeOfFirstPeriod] [int] NULL,
	[HasBreastChangesDuringPregnancy] [bit] NOT NULL,
	[BreastChangeDescription] [nvarchar](max) NULL,
	[NumPregnancies] [int] NULL,
	[NumLiveChildren] [int] NULL,
	[HasHadCancer] [bit] NOT NULL,
	[CancerDescription] [nvarchar](max) NULL,
	[HasUsedBirthControl] [bit] NOT NULL,
	[BirthControlDescription] [nvarchar](max) NULL,
	[ConfidentialCommunicationMethodId] [int] NOT NULL,
	[DoesFeelSafeAtHome] [bit] NULL,
	[DoesWantToSpeakWithoutPartnerPresent] [bit] NULL,
	[HasHadToSeekHelpWithSafetyConcern] [bit] NULL,
	[IsPartnerSupportiveOfGoals] [bit] NULL,
	[DoesHaveHistoryOfDepressionOrAnxiety] [bit] NOT NULL,
	[DoesSymptomManagementWorkWell] [bit] NOT NULL,
	[HowSymptomsAreManaged] [nvarchar](max) NULL,
	[IsInterestedInReceivingResources] [bit] NOT NULL,
 CONSTRAINT [PK_ParentMedicalHistory] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[PersonAddresses]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[PersonAddresses](
	[Id] [uniqueidentifier] NOT NULL,
	[AddressId] [uniqueidentifier] NOT NULL,
	[IsPrimary] [bit] NOT NULL,
	[PersonId] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_PersonAddresses] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[PersonEmails]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[PersonEmails](
	[Id] [uniqueidentifier] NOT NULL,
	[EmailId] [uniqueidentifier] NOT NULL,
	[IsPrimary] [bit] NOT NULL,
	[PersonId] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_PersonEmails] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[PersonImages]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[PersonImages](
	[Id] [uniqueidentifier] NOT NULL,
	[ImageId] [uniqueidentifier] NOT NULL,
	[IsPrimary] [bit] NOT NULL,
	[PersonId] [uniqueidentifier] NOT NULL,
	[CreateDate] [datetimeoffset](7) NULL,
	[CreateUserId] [uniqueidentifier] NULL,
 CONSTRAINT [PK_PersonImages] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[PersonNotes]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[PersonNotes](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[EditDate] [datetime2](7) NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[Note] [nvarchar](max) NOT NULL,
	[PersonId] [uniqueidentifier] NOT NULL,
	[NoteTypeId] [int] NOT NULL,
 CONSTRAINT [PK_PersonNotes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[PersonPersonTypes]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[PersonPersonTypes](
	[Id] [uniqueidentifier] NOT NULL,
	[PersonId] [uniqueidentifier] NOT NULL,
	[PersonTypeId] [int] NOT NULL,
 CONSTRAINT [PK_PersonPersonTypes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[PersonPhones]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[PersonPhones](
	[Id] [uniqueidentifier] NOT NULL,
	[IsPrimary] [bit] NOT NULL,
	[PersonId] [uniqueidentifier] NOT NULL,
	[PhoneId] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_PersonPhones] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[PersonProviders]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[PersonProviders](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[EditDate] [datetime2](7) NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[PersonId] [uniqueidentifier] NOT NULL,
	[SubscriberProviderId] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_PersonProviders] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[PersonProviderTypes]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[PersonProviderTypes](
	[Id] [uniqueidentifier] NOT NULL,
	[PersonId] [uniqueidentifier] NOT NULL,
	[ProviderTypeId] [int] NOT NULL,
 CONSTRAINT [PK_PersonProviderTypes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Persons]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Persons](
	[Id] [uniqueidentifier] NOT NULL,
	[BirthDate] [datetimeoffset](7) NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[EditDate] [datetime2](7) NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[GenderId] [int] NULL,
	[SSN] [nvarchar](11) NULL,
	[Allergies] [nvarchar](max) NULL,
	[MedicaidNumber] [nvarchar](max) NULL,
	[PreferredPronounId] [int] NULL,
	[IsInactive] [bit] NOT NULL,
 CONSTRAINT [PK_Persons] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[PersonTypes]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[PersonTypes](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_PersonTypes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Phones]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Phones](
	[Id] [uniqueidentifier] NOT NULL,
	[LineTypeId] [int] NOT NULL,
	[Number] [nvarchar](50) NULL,
	[PhoneTypeId] [int] NOT NULL,
 CONSTRAINT [PK_Phones] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[PhoneTypes]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[PhoneTypes](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_PhoneTypes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[PostPartumScaleQuestions]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[PostPartumScaleQuestions](
	[Id] [int] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_PostPartumScaleQuestions] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Prefixes]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Prefixes](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_Prefixes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ProcedureCustomOptions]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ProcedureCustomOptions](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[EditDate] [datetime2](7) NULL,
	[SubscriberId] [uniqueidentifier] NOT NULL,
	[Text] [nvarchar](max) NULL,
	[IsDeleted] [bit] NOT NULL,
	[IsParentEntered] [bit] NOT NULL,
 CONSTRAINT [PK_ProcedureCustomOptions] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ProcedureLocations]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ProcedureLocations](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_ProcedureLocations] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Procedures]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Procedures](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[ProcedureLocationId] [int] NULL,
	[ProcedureTypeId] [int] NOT NULL,
	[OtherText] [nvarchar](100) NULL,
	[PersonId] [uniqueidentifier] NOT NULL,
	[Year] [int] NULL,
	[ProcedureCustomOptionId] [uniqueidentifier] NULL,
	[AntiDuplicationIndex] [nvarchar](200) NOT NULL,
 CONSTRAINT [PK_Procedures] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ProcedureTypes]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ProcedureTypes](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[IsForChild] [bit] NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_ProcedureTypes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ProductCustomOptions]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ProductCustomOptions](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[EditDate] [datetime2](7) NULL,
	[SubscriberId] [uniqueidentifier] NOT NULL,
	[Text] [nvarchar](max) NULL,
	[IsDeleted] [bit] NOT NULL,
	[IsParentEntered] [bit] NOT NULL,
 CONSTRAINT [PK_ProductCustomOptions] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Products]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Products](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[EvaluationId] [uniqueidentifier] NOT NULL,
	[ProductTypeId] [int] NOT NULL,
	[OtherText] [nvarchar](100) NULL,
	[ProductCustomOptionId] [uniqueidentifier] NULL,
	[AntiDuplicationIndex] [nvarchar](200) NOT NULL,
 CONSTRAINT [PK_Products] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ProductTypes]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ProductTypes](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_ProductTypes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Pronouns]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Pronouns](
	[Id] [int] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_Pronouns] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ProviderTypes]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ProviderTypes](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_ProviderTypes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[PumpBrandCustomOptions]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[PumpBrandCustomOptions](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[EditDate] [datetime2](7) NULL,
	[SubscriberId] [uniqueidentifier] NOT NULL,
	[Text] [nvarchar](max) NULL,
	[IsDeleted] [bit] NOT NULL,
	[IsParentEntered] [bit] NOT NULL,
 CONSTRAINT [PK_PumpBrandCustomOptions] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[PumpBrands]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[PumpBrands](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_PumpBrands] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[PumpTypes]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[PumpTypes](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_PumpTypes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ReferrerPersons]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ReferrerPersons](
	[Id] [uniqueidentifier] NOT NULL,
	[PersonId] [uniqueidentifier] NOT NULL,
	[ReferrerId] [uniqueidentifier] NOT NULL,
	[SubscriberId] [uniqueidentifier] NULL,
 CONSTRAINT [PK_ReferrerPersons] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Relationships]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Relationships](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_Relationships] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[SelfPayTypes]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SelfPayTypes](
	[Id] [int] NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_SelfPayTypes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[SkinTurgor]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SkinTurgor](
	[Id] [int] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_SkinTurgor] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[StatementConcerns]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[StatementConcerns](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[ConcernId] [int] NOT NULL,
	[OtherText] [nvarchar](100) NULL,
	[StatementId] [uniqueidentifier] NOT NULL,
	[ConcernCustomOptionId] [uniqueidentifier] NULL,
	[AntiDuplicationIndex] [nvarchar](200) NOT NULL,
 CONSTRAINT [PK_StatementConcerns] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Statements]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Statements](
	[Id] [uniqueidentifier] NOT NULL,
	[IsRetrningToWork] [bit] NULL,
	[IsWorkSafeToPump] [bit] NULL,
	[OtherNotes] [nvarchar](max) NULL,
	[LongTermBreastfeedingGoals] [nvarchar](max) NULL,
	[ShortTermBreastfeedingGoals] [nvarchar](max) NULL,
	[Employment] [nvarchar](max) NULL,
	[NumWeeksUntilReturningToWork] [int] NULL,
 CONSTRAINT [PK_Statements] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[StoolColorCustomOptions]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[StoolColorCustomOptions](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[EditDate] [datetime2](7) NULL,
	[SubscriberId] [uniqueidentifier] NOT NULL,
	[Text] [nvarchar](max) NULL,
	[IsDeleted] [bit] NOT NULL,
	[IsParentEntered] [bit] NOT NULL,
 CONSTRAINT [PK_StoolColorCustomOptions] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[StoolColors]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[StoolColors](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[EvaluationId] [uniqueidentifier] NOT NULL,
	[StoolColorTypeId] [int] NOT NULL,
	[OtherText] [nvarchar](100) NULL,
	[StoolColorCustomOptionId] [uniqueidentifier] NULL,
	[AntiDuplicationIndex] [nvarchar](200) NOT NULL,
 CONSTRAINT [PK_StoolColors] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[StoolColorTypes]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[StoolColorTypes](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_StoolColorTypes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[StoolConsistencies]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[StoolConsistencies](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[EvaluationId] [uniqueidentifier] NOT NULL,
	[StoolConsistencyTypeId] [int] NOT NULL,
	[OtherText] [nvarchar](100) NULL,
	[StoolConsistencyCustomOptionId] [uniqueidentifier] NULL,
	[AntiDuplicationIndex] [nvarchar](200) NOT NULL,
 CONSTRAINT [PK_StoolConsistencies] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[StoolConsistencyCustomOptions]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[StoolConsistencyCustomOptions](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[EditDate] [datetime2](7) NULL,
	[SubscriberId] [uniqueidentifier] NOT NULL,
	[Text] [nvarchar](max) NULL,
	[IsDeleted] [bit] NOT NULL,
	[IsParentEntered] [bit] NOT NULL,
 CONSTRAINT [PK_StoolConsistencyCustomOptions] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[StoolConsistencyTypes]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[StoolConsistencyTypes](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_StoolConsistencyTypes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[SubscriberBreastPumps]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SubscriberBreastPumps](
	[Id] [uniqueidentifier] NOT NULL,
	[BrandId] [int] NULL,
	[EnteredIntoInventoryDate] [datetime2](7) NOT NULL,
	[PumpBrandId] [int] NOT NULL,
	[PumpTypeId] [int] NOT NULL,
	[RemovedFromInventoryDate] [datetime2](7) NULL,
	[SerialNumber] [nvarchar](max) NULL,
	[TypeId] [int] NULL,
	[SubscriberId] [uniqueidentifier] NOT NULL,
	[Name] [nvarchar](max) NULL,
 CONSTRAINT [PK_SubscriberBreastPumps] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[SubscriberParents]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SubscriberParents](
	[Id] [uniqueidentifier] NOT NULL,
	[ParentId] [uniqueidentifier] NOT NULL,
	[SubscriberId] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_SubscriberParents] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Subscribers]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Subscribers](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[EditDate] [datetime2](7) NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[IsPaid] [bit] NOT NULL,
	[NPI] [nvarchar](50) NULL,
	[Name] [nvarchar](256) NOT NULL,
	[PayPalId] [nvarchar](150) NULL,
	[LogoUrl] [nvarchar](max) NULL,
	[AdminSubscriberUserId] [uniqueidentifier] NULL,
	[StripeCustomerId] [nvarchar](max) NULL,
	[TrialPeriodExpirationDate] [datetimeoffset](7) NOT NULL,
	[AllowLCsToViewOtherLCsVisits] [bit] NOT NULL,
	[IsCancelled] [bit] NOT NULL,
	[CustomSubscriptionLevelCost] [decimal](18, 2) NOT NULL,
	[CustomSubscriptionLevelNumberOfLicenses] [int] NOT NULL,
	[IsOnCustomSubscriptionLevel] [bit] NOT NULL,
	[SubscriptionTermId] [int] NOT NULL,
	[AnnualPaymentStartMonth] [int] NOT NULL,
	[DefaultWeightUnitId] [int] NOT NULL,
	[PaymentStartDate] [int] NOT NULL,
	[IsOnFreeTrial] [bit] NOT NULL,
	[EIN] [nvarchar](max) NULL,
	[CalendarId] [uniqueidentifier] NULL,
	[AllowLCsToViewOtherLCsClients] [bit] NOT NULL,
	[IsTranscriptionEnabled] [bit] NOT NULL,
	[IsAutoRenewEnabled] [bit] NOT NULL,
 CONSTRAINT [PK_Subscribers] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[SubscriberSettings]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SubscriberSettings](
	[Id] [uniqueidentifier] NOT NULL,
	[IsCashEnabled] [bit] NOT NULL,
	[IsCheckEnabled] [bit] NOT NULL,
	[IsCreditCardEnabled] [bit] NOT NULL,
	[IsInsuranceEnabled] [bit] NOT NULL,
	[IsMedicareEnabled] [bit] NOT NULL,
	[IsHsaFsaEnabled] [bit] NOT NULL,
 CONSTRAINT [PK_SubscriberSettings] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[SubscriberUsers]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SubscriberUsers](
	[Id] [uniqueidentifier] NOT NULL,
	[SubscriberId] [uniqueidentifier] NOT NULL,
	[UserId] [uniqueidentifier] NOT NULL,
	[IsActive] [bit] NOT NULL,
	[IsParent] [bit] NOT NULL,
	[Signature] [nvarchar](max) NULL,
	[IsOnMilkNotesPro] [bit] NOT NULL,
	[HasAgreedToVideoCareResponsibilities] [bit] NOT NULL,
	[IsAdmin] [bit] NOT NULL,
	[CalendarId] [uniqueidentifier] NULL,
	[ContactId] [nvarchar](250) NULL,
 CONSTRAINT [PK_SubscriberUsers] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SuckCustomOptions](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[EditDate] [datetime2](7) NULL,
	[SubscriberId] [uniqueidentifier] NOT NULL,
	[Text] [nvarchar](max) NULL,
	[IsDeleted] [bit] NOT NULL,
	[IsParentEntered] [bit] NOT NULL,
 CONSTRAINT [PK_SuckCustomOptions] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SuckObservations](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[EvaluationId] [uniqueidentifier] NOT NULL,
	[SuckTypeId] [int] NOT NULL,
	[OtherText] [nvarchar](100) NULL,
	[SuckCustomOptionId] [uniqueidentifier] NULL,
	[AntiDuplicationIndex] [nvarchar](200) NOT NULL,
 CONSTRAINT [PK_SuckObservations] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SuckTypes](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_SuckTypes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Suffixes](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_Suffixes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SupportPersons](
	[Id] [uniqueidentifier] NOT NULL,
	[PersonId] [uniqueidentifier] NOT NULL,
	[SupporterId] [uniqueidentifier] NOT NULL,
	[RelationshipId] [int] NOT NULL,
 CONSTRAINT [PK_SupportPersons] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[TemplateTypes](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[Value] [nvarchar](max) NULL,
	[Name] [nvarchar](500) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_TemplateTypes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[TimedEvents](
	[Id] [uniqueidentifier] NOT NULL,
	[BirthHistoryId] [uniqueidentifier] NOT NULL,
	[Length] [decimal](18, 2) NOT NULL,
	[TimedEventTypeId] [int] NOT NULL,
	[SubLength] [decimal](18, 2) NULL,
 CONSTRAINT [PK_TimedEvents] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[TimedEventTypes](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[IsForChild] [bit] NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_TimedEventTypes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[TongueCustomOptions](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[EditDate] [datetime2](7) NULL,
	[SubscriberId] [uniqueidentifier] NOT NULL,
	[Text] [nvarchar](max) NULL,
	[IsDeleted] [bit] NOT NULL,
	[IsParentEntered] [bit] NOT NULL,
 CONSTRAINT [PK_TongueCustomOptions] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[TongueObservations](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[EvaluationId] [uniqueidentifier] NOT NULL,
	[TongueTypeId] [int] NOT NULL,
	[OtherText] [nvarchar](100) NULL,
	[TongueCustomOptionId] [uniqueidentifier] NULL,
	[AntiDuplicationIndex] [nvarchar](200) NOT NULL,
 CONSTRAINT [PK_TongueObservations] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[TongueTypes](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_TongueTypes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[VisitChildConcerns](
	[Id] [uniqueidentifier] NOT NULL,
	[ChildId] [uniqueidentifier] NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[ChildConcernId] [int] NOT NULL,
	[OtherText] [nvarchar](100) NULL,
	[VisitId] [uniqueidentifier] NOT NULL,
	[CustomOptionId] [uniqueidentifier] NULL,
	[AntiDuplicationIndex] [nvarchar](200) NULL,
 CONSTRAINT [PK_VisitChildConcerns] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[VisitChildNotes]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[VisitChildNotes](
	[Id] [uniqueidentifier] NOT NULL,
	[ChildId] [uniqueidentifier] NOT NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[EditDate] [datetime2](7) NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[EnteredByPersonId] [uniqueidentifier] NOT NULL,
	[Note] [nvarchar](max) NOT NULL,
	[VisitId] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_VisitChildNotes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[VisitNotes]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[VisitNotes](
	[Id] [uniqueidentifier] NOT NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[EditDate] [datetime2](7) NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[Note] [nvarchar](max) NOT NULL,
	[VisitId] [uniqueidentifier] NOT NULL,
	[EnteredByPersonId] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_VisitNotes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[VisitPostPartumScaleQuestionScores]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[VisitPostPartumScaleQuestionScores](
	[Id] [uniqueidentifier] NOT NULL,
	[VisitId] [uniqueidentifier] NOT NULL,
	[PostPartumScaleQuestionId] [int] NOT NULL,
	[Score] [int] NOT NULL,
 CONSTRAINT [PK_VisitPostPartumScaleQuestionScores] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Visits]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Visits](
	[Id] [uniqueidentifier] NOT NULL,
	[ConsultantId] [uniqueidentifier] NOT NULL,
	[CreateDate] [datetime2](7) NOT NULL,
	[CreateUserId] [uniqueidentifier] NOT NULL,
	[DistanceTraveled] [decimal](18, 2) NULL,
	[EditDate] [datetime2](7) NULL,
	[EditReason] [nvarchar](max) NULL,
	[EditUserId] [uniqueidentifier] NULL,
	[EndDateTime] [datetimeoffset](7) NULL,
	[ParentId] [uniqueidentifier] NOT NULL,
	[StartDateTime] [datetimeoffset](7) NOT NULL,
	[VisitTypeId] [int] NULL,
	[VisitStatusId] [int] NOT NULL,
	[SubscriberId] [uniqueidentifier] NOT NULL,
	[HasAgreedToHipaaTerms] [bit] NOT NULL,
	[ClosedDateTime] [datetimeoffset](7) NULL,
	[ParentHistoryNotes] [nvarchar](max) NULL,
	[ParentProfileNotes] [nvarchar](max) NULL,
	[HasAgreedToHipaaConsent] [bit] NOT NULL,
	[MedicalReportNotes] [nvarchar](max) NULL,
	[PreferredCommunicationTypeId] [int] NULL,
	[AppointmentTypeId] [uniqueidentifier] NOT NULL,
	[PostBuffer] [int] NOT NULL,
	[PreBuffer] [int] NOT NULL,
	[Price] [int] NULL,
	[IsHsaFsa] [bit] NOT NULL,
	[IsInsurance] [bit] NOT NULL,
	[IsMedicaid] [bit] NOT NULL,
	[IsSelfPay] [bit] NOT NULL,
	[SelfPayTypeId] [int] NOT NULL,
	[ParentConcerns] [nvarchar](max) NULL,
	[VisitAcceptanceStatusId] [int] NOT NULL,
	[VisitNotAcceptedReason] [nvarchar](max) NULL,
	[IsCancelled] [bit] NOT NULL,
	[VisitCancellationOtherReason] [nvarchar](max) NULL,
	[VisitCancellationReasonId] [int] NOT NULL,
	[IsIntakeComplete] [bit] NOT NULL,
 CONSTRAINT [PK_Visits] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[VisitTypes]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[VisitTypes](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_VisitTypes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[WeightTypes]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[WeightTypes](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_WeightTypes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[WeightUnits]    Script Date: 6/26/2025 11:55:31 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[WeightUnits](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[IsDeleted] [bit] NOT NULL,
	[Name] [nvarchar](500) NOT NULL,
	[SortOrder] [int] NOT NULL,
 CONSTRAINT [PK_WeightUnits] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
ALTER TABLE [dbo].[AppearanceCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsDeleted]
GO
ALTER TABLE [dbo].[AppearanceCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsParentEntered]
GO
ALTER TABLE [dbo].[Appearances] ADD  DEFAULT (N'') FOR [AntiDuplicationIndex]
GO
ALTER TABLE [dbo].[AppointmentTypes] ADD  DEFAULT ((0)) FOR [IsDurationShownOnPublicCalendar]
GO
ALTER TABLE [dbo].[AppointmentTypes] ADD  DEFAULT ((0)) FOR [BlackoutDurationMinutes]
GO
ALTER TABLE [dbo].[BirthHistories] ADD  DEFAULT ((0)) FOR [WasChildResuscitated]
GO
ALTER TABLE [dbo].[BirthLocationNameCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsDeleted]
GO
ALTER TABLE [dbo].[BirthLocationNameCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsParentEntered]
GO
ALTER TABLE [dbo].[BreastCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsDeleted]
GO
ALTER TABLE [dbo].[BreastCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsParentEntered]
GO
ALTER TABLE [dbo].[BreastObservations] ADD  DEFAULT (N'') FOR [AntiDuplicationIndex]
GO
ALTER TABLE [dbo].[BreastPumpRentals] ADD  DEFAULT ('00000000-0000-0000-0000-000000000000') FOR [SubscriberBreastPumpId]
GO
ALTER TABLE [dbo].[BreastPumpRentals] ADD  DEFAULT ('0001-01-01T00:00:00.000') FOR [DueDate]
GO
ALTER TABLE [dbo].[ChildStateCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsDeleted]
GO
ALTER TABLE [dbo].[ChildStateCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsParentEntered]
GO
ALTER TABLE [dbo].[ChildStates] ADD  DEFAULT (N'') FOR [AntiDuplicationIndex]
GO
ALTER TABLE [dbo].[ComplicationCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsDeleted]
GO
ALTER TABLE [dbo].[ComplicationCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsParentEntered]
GO
ALTER TABLE [dbo].[Complications] ADD  DEFAULT (N'') FOR [AntiDuplicationIndex]
GO
ALTER TABLE [dbo].[ConcernCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsDeleted]
GO
ALTER TABLE [dbo].[ConcernCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsParentEntered]
GO
ALTER TABLE [dbo].[Diagnoses] ADD  DEFAULT ((0)) FOR [HumanSystemId]
GO
ALTER TABLE [dbo].[Diagnoses] ADD  DEFAULT (N'') FOR [AntiDuplicationIndex]
GO
ALTER TABLE [dbo].[DiagnosisTypeCategorizedCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsDeleted]
GO
ALTER TABLE [dbo].[DiagnosisTypeCategorizedCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsParentEntered]
GO
ALTER TABLE [dbo].[DiagnosisTypes] ADD  DEFAULT ((0)) FOR [HumanSystemId]
GO
ALTER TABLE [dbo].[DocumentResources] ADD  DEFAULT ((0)) FOR [IsFolder]
GO
ALTER TABLE [dbo].[DocumentResources] ADD  DEFAULT ((0)) FOR [SortOrder]
GO
ALTER TABLE [dbo].[FeedingMethodCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsDeleted]
GO
ALTER TABLE [dbo].[FeedingMethodCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsParentEntered]
GO
ALTER TABLE [dbo].[FeedingMethods] ADD  DEFAULT (N'') FOR [AntiDuplicationIndex]
GO
ALTER TABLE [dbo].[FeedingPositionCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsDeleted]
GO
ALTER TABLE [dbo].[FeedingPositionCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsParentEntered]
GO
ALTER TABLE [dbo].[FeedingPositions] ADD  DEFAULT (N'') FOR [AntiDuplicationIndex]
GO
ALTER TABLE [dbo].[FlangeSizeCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsDeleted]
GO
ALTER TABLE [dbo].[FlangeSizeCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsParentEntered]
GO
ALTER TABLE [dbo].[ICD10CodeCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsDeleted]
GO
ALTER TABLE [dbo].[ICD10CodeCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsParentEntered]
GO
ALTER TABLE [dbo].[ICD10s] ADD  DEFAULT (N'') FOR [AntiDuplicationIndex]
GO
ALTER TABLE [dbo].[Images] ADD  DEFAULT (N'') FOR [ContentType]
GO
ALTER TABLE [dbo].[Images] ADD  DEFAULT (N'') FOR [ThumbnailPath]
GO
ALTER TABLE [dbo].[InterventionCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsDeleted]
GO
ALTER TABLE [dbo].[InterventionCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsParentEntered]
GO
ALTER TABLE [dbo].[Interventions] ADD  DEFAULT (N'') FOR [AntiDuplicationIndex]
GO
ALTER TABLE [dbo].[Kids] ADD  DEFAULT ((0)) FOR [IsDeleted]
GO
ALTER TABLE [dbo].[LactationEvaluationCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsDeleted]
GO
ALTER TABLE [dbo].[LactationEvaluationCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsParentEntered]
GO
ALTER TABLE [dbo].[LactationEvaluations] ADD  DEFAULT (N'') FOR [AntiDuplicationIndex]
GO
ALTER TABLE [dbo].[LipCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsDeleted]
GO
ALTER TABLE [dbo].[LipCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsParentEntered]
GO
ALTER TABLE [dbo].[LipObservations] ADD  DEFAULT (N'') FOR [AntiDuplicationIndex]
GO
ALTER TABLE [dbo].[MedicationCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsDeleted]
GO
ALTER TABLE [dbo].[MedicationCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsParentEntered]
GO
ALTER TABLE [dbo].[Medications] ADD  DEFAULT ((0)) FOR [IsDoseComplete]
GO
ALTER TABLE [dbo].[NippleCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsDeleted]
GO
ALTER TABLE [dbo].[NippleCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsParentEntered]
GO
ALTER TABLE [dbo].[NippleObservations] ADD  DEFAULT (N'') FOR [AntiDuplicationIndex]
GO
ALTER TABLE [dbo].[NippleShieldCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsDeleted]
GO
ALTER TABLE [dbo].[NippleShieldCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsParentEntered]
GO
ALTER TABLE [dbo].[PalateCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsDeleted]
GO
ALTER TABLE [dbo].[PalateCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsParentEntered]
GO
ALTER TABLE [dbo].[PalateObservations] ADD  DEFAULT (N'') FOR [AntiDuplicationIndex]
GO
ALTER TABLE [dbo].[ParentMedicalHistory] ADD  DEFAULT ((0)) FOR [ConfidentialCommunicationMethodId]
GO
ALTER TABLE [dbo].[ParentMedicalHistory] ADD  DEFAULT ((0)) FOR [DoesHaveHistoryOfDepressionOrAnxiety]
GO
ALTER TABLE [dbo].[ParentMedicalHistory] ADD  DEFAULT ((0)) FOR [DoesSymptomManagementWorkWell]
GO
ALTER TABLE [dbo].[ParentMedicalHistory] ADD  DEFAULT ((0)) FOR [IsInterestedInReceivingResources]
GO
ALTER TABLE [dbo].[Persons] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsInactive]
GO
ALTER TABLE [dbo].[ProcedureCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsDeleted]
GO
ALTER TABLE [dbo].[ProcedureCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsParentEntered]
GO
ALTER TABLE [dbo].[Procedures] ADD  DEFAULT (N'') FOR [AntiDuplicationIndex]
GO
ALTER TABLE [dbo].[ProductCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsDeleted]
GO
ALTER TABLE [dbo].[ProductCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsParentEntered]
GO
ALTER TABLE [dbo].[Products] ADD  DEFAULT (N'') FOR [AntiDuplicationIndex]
GO
ALTER TABLE [dbo].[PumpBrandCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsDeleted]
GO
ALTER TABLE [dbo].[PumpBrandCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsParentEntered]
GO
ALTER TABLE [dbo].[StatementConcerns] ADD  DEFAULT (N'') FOR [AntiDuplicationIndex]
GO
ALTER TABLE [dbo].[StoolColorCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsDeleted]
GO
ALTER TABLE [dbo].[StoolColorCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsParentEntered]
GO
ALTER TABLE [dbo].[StoolColors] ADD  DEFAULT (N'') FOR [AntiDuplicationIndex]
GO
ALTER TABLE [dbo].[StoolConsistencies] ADD  DEFAULT (N'') FOR [AntiDuplicationIndex]
GO
ALTER TABLE [dbo].[StoolConsistencyCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsDeleted]
GO
ALTER TABLE [dbo].[StoolConsistencyCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsParentEntered]
GO
ALTER TABLE [dbo].[SubscriberBreastPumps] ADD  DEFAULT ('00000000-0000-0000-0000-000000000000') FOR [SubscriberId]
GO
ALTER TABLE [dbo].[Subscribers] ADD  DEFAULT ('0001-01-01T00:00:00.000+00:00') FOR [TrialPeriodExpirationDate]
GO
ALTER TABLE [dbo].[Subscribers] ADD  DEFAULT ((0)) FOR [AllowLCsToViewOtherLCsVisits]
GO
ALTER TABLE [dbo].[Subscribers] ADD  DEFAULT ((0)) FOR [IsCancelled]
GO
ALTER TABLE [dbo].[Subscribers] ADD  DEFAULT ((0.0)) FOR [CustomSubscriptionLevelCost]
GO
ALTER TABLE [dbo].[Subscribers] ADD  DEFAULT ((0)) FOR [CustomSubscriptionLevelNumberOfLicenses]
GO
ALTER TABLE [dbo].[Subscribers] ADD  DEFAULT ((0)) FOR [IsOnCustomSubscriptionLevel]
GO
ALTER TABLE [dbo].[Subscribers] ADD  DEFAULT ((1)) FOR [SubscriptionTermId]
GO
ALTER TABLE [dbo].[Subscribers] ADD  DEFAULT ((-1)) FOR [AnnualPaymentStartMonth]
GO
ALTER TABLE [dbo].[Subscribers] ADD  DEFAULT ((2)) FOR [DefaultWeightUnitId]
GO
ALTER TABLE [dbo].[Subscribers] ADD  DEFAULT ((-1)) FOR [PaymentStartDate]
GO
ALTER TABLE [dbo].[Subscribers] ADD  DEFAULT ((0)) FOR [IsOnFreeTrial]
GO
ALTER TABLE [dbo].[Subscribers] ADD  DEFAULT ((0)) FOR [AllowLCsToViewOtherLCsClients]
GO
ALTER TABLE [dbo].[Subscribers] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsTranscriptionEnabled]
GO
ALTER TABLE [dbo].[Subscribers] ADD  DEFAULT (CONVERT([bit],(1))) FOR [IsAutoRenewEnabled]
GO
ALTER TABLE [dbo].[SubscriberUsers] ADD  DEFAULT ((1)) FOR [IsActive]
GO
ALTER TABLE [dbo].[SubscriberUsers] ADD  DEFAULT ((0)) FOR [IsParent]
GO
ALTER TABLE [dbo].[SubscriberUsers] ADD  DEFAULT ((0)) FOR [IsOnMilkNotesPro]
GO
ALTER TABLE [dbo].[SubscriberUsers] ADD  DEFAULT ((0)) FOR [HasAgreedToVideoCareResponsibilities]
GO
ALTER TABLE [dbo].[SubscriberUsers] ADD  DEFAULT ((0)) FOR [IsAdmin]
GO
ALTER TABLE [dbo].[SuckCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsDeleted]
GO
ALTER TABLE [dbo].[SuckCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsParentEntered]
GO
ALTER TABLE [dbo].[SuckObservations] ADD  DEFAULT (N'') FOR [AntiDuplicationIndex]
GO
ALTER TABLE [dbo].[SupportPersons] ADD  DEFAULT ((0)) FOR [RelationshipId]
GO
ALTER TABLE [dbo].[TongueCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsDeleted]
GO
ALTER TABLE [dbo].[TongueCustomOptions] ADD  DEFAULT (CONVERT([bit],(0))) FOR [IsParentEntered]
GO
ALTER TABLE [dbo].[TongueObservations] ADD  DEFAULT (N'') FOR [AntiDuplicationIndex]
GO
ALTER TABLE [dbo].[VisitNotes] ADD  DEFAULT ('00000000-0000-0000-0000-000000000000') FOR [EnteredByPersonId]
GO
ALTER TABLE [dbo].[Visits] ADD  DEFAULT ('00000000-0000-0000-0000-000000000000') FOR [ParentId]
GO
ALTER TABLE [dbo].[Visits] ADD  DEFAULT ((0)) FOR [VisitStatusId]
GO
ALTER TABLE [dbo].[Visits] ADD  DEFAULT ('00000000-0000-0000-0000-000000000000') FOR [SubscriberId]
GO
ALTER TABLE [dbo].[Visits] ADD  DEFAULT ((0)) FOR [HasAgreedToHipaaTerms]
GO
ALTER TABLE [dbo].[Visits] ADD  DEFAULT ((0)) FOR [HasAgreedToHipaaConsent]
GO
ALTER TABLE [dbo].[Visits] ADD  DEFAULT ('00000000-0000-0000-0000-000000000000') FOR [AppointmentTypeId]
GO
ALTER TABLE [dbo].[Visits] ADD  DEFAULT ((0)) FOR [PostBuffer]
GO
ALTER TABLE [dbo].[Visits] ADD  DEFAULT ((0)) FOR [PreBuffer]
GO
ALTER TABLE [dbo].[Visits] ADD  DEFAULT ((0)) FOR [IsHsaFsa]
GO
ALTER TABLE [dbo].[Visits] ADD  DEFAULT ((0)) FOR [IsInsurance]
GO
ALTER TABLE [dbo].[Visits] ADD  DEFAULT ((0)) FOR [IsMedicaid]
GO
ALTER TABLE [dbo].[Visits] ADD  DEFAULT ((0)) FOR [IsSelfPay]
GO
ALTER TABLE [dbo].[Visits] ADD  DEFAULT ((0)) FOR [SelfPayTypeId]
GO
ALTER TABLE [dbo].[Visits] ADD  DEFAULT ((0)) FOR [VisitAcceptanceStatusId]
GO
ALTER TABLE [dbo].[Visits] ADD  DEFAULT ((0)) FOR [IsCancelled]
GO
ALTER TABLE [dbo].[Visits] ADD  DEFAULT ((0)) FOR [VisitCancellationReasonId]
GO
ALTER TABLE [dbo].[Visits] ADD  DEFAULT ((0)) FOR [IsIntakeComplete]
GO
ALTER TABLE [dbo].[Addresses]  WITH CHECK ADD  CONSTRAINT [FK_Addresses_AddressTypes_AddressTypeId] FOREIGN KEY([AddressTypeId])
REFERENCES [dbo].[AddressTypes] ([Id])
GO
ALTER TABLE [dbo].[Addresses] CHECK CONSTRAINT [FK_Addresses_AddressTypes_AddressTypeId]
GO
ALTER TABLE [dbo].[AppearanceCustomOptions]  WITH CHECK ADD  CONSTRAINT [FK_AppearanceCustomOptions_Subscribers_SubscriberId] FOREIGN KEY([SubscriberId])
REFERENCES [dbo].[Subscribers] ([Id])
GO
ALTER TABLE [dbo].[AppearanceCustomOptions] CHECK CONSTRAINT [FK_AppearanceCustomOptions_Subscribers_SubscriberId]
GO
ALTER TABLE [dbo].[Appearances]  WITH CHECK ADD  CONSTRAINT [FK_Appearances_AppearanceCustomOptions_AppearanceCustomOptionId] FOREIGN KEY([AppearanceCustomOptionId])
REFERENCES [dbo].[AppearanceCustomOptions] ([Id])
GO
ALTER TABLE [dbo].[Appearances] CHECK CONSTRAINT [FK_Appearances_AppearanceCustomOptions_AppearanceCustomOptionId]
GO
ALTER TABLE [dbo].[Appearances]  WITH CHECK ADD  CONSTRAINT [FK_Appearances_AppearanceTypes_AppearanceTypeId] FOREIGN KEY([AppearanceTypeId])
REFERENCES [dbo].[AppearanceTypes] ([Id])
GO
ALTER TABLE [dbo].[Appearances] CHECK CONSTRAINT [FK_Appearances_AppearanceTypes_AppearanceTypeId]
GO
ALTER TABLE [dbo].[Appearances]  WITH CHECK ADD  CONSTRAINT [FK_Appearances_KidEvaluations_EvaluationId] FOREIGN KEY([EvaluationId])
REFERENCES [dbo].[KidEvaluations] ([Id])
GO
ALTER TABLE [dbo].[Appearances] CHECK CONSTRAINT [FK_Appearances_KidEvaluations_EvaluationId]
GO
ALTER TABLE [dbo].[AppointmentTypes]  WITH CHECK ADD  CONSTRAINT [FK_AppointmentTypes_ConsultLocations_ConsultLocationId] FOREIGN KEY([ConsultLocationId])
REFERENCES [dbo].[ConsultLocations] ([Id])
GO
ALTER TABLE [dbo].[AppointmentTypes] CHECK CONSTRAINT [FK_AppointmentTypes_ConsultLocations_ConsultLocationId]
GO
ALTER TABLE [dbo].[AppointmentTypes]  WITH CHECK ADD  CONSTRAINT [FK_AppointmentTypes_Subscribers_SubscriberId] FOREIGN KEY([SubscriberId])
REFERENCES [dbo].[Subscribers] ([Id])
GO
ALTER TABLE [dbo].[AppointmentTypes] CHECK CONSTRAINT [FK_AppointmentTypes_Subscribers_SubscriberId]
GO
ALTER TABLE [dbo].[BirthHistories]  WITH CHECK ADD  CONSTRAINT [FK_BirthHistories_BirthLocationNameCustomOptions_BirthLocationNameId] FOREIGN KEY([BirthLocationNameId])
REFERENCES [dbo].[BirthLocationNameCustomOptions] ([Id])
GO
ALTER TABLE [dbo].[BirthHistories] CHECK CONSTRAINT [FK_BirthHistories_BirthLocationNameCustomOptions_BirthLocationNameId]
GO
ALTER TABLE [dbo].[BirthHistories]  WITH CHECK ADD  CONSTRAINT [FK_BirthHistories_BirthLocations_BirthLocationId] FOREIGN KEY([BirthLocationId])
REFERENCES [dbo].[BirthLocations] ([Id])
GO
ALTER TABLE [dbo].[BirthHistories] CHECK CONSTRAINT [FK_BirthHistories_BirthLocations_BirthLocationId]
GO
ALTER TABLE [dbo].[BirthHistories]  WITH CHECK ADD  CONSTRAINT [FK_BirthHistories_CesareanReasons_CesareanReasonId] FOREIGN KEY([CesareanReasonId])
REFERENCES [dbo].[CesareanReasons] ([Id])
GO
ALTER TABLE [dbo].[BirthHistories] CHECK CONSTRAINT [FK_BirthHistories_CesareanReasons_CesareanReasonId]
GO
ALTER TABLE [dbo].[BirthHistories]  WITH CHECK ADD  CONSTRAINT [FK_BirthHistories_DeliveryTypes_DeliveryTypeId] FOREIGN KEY([DeliveryTypeId])
REFERENCES [dbo].[DeliveryTypes] ([Id])
GO
ALTER TABLE [dbo].[BirthHistories] CHECK CONSTRAINT [FK_BirthHistories_DeliveryTypes_DeliveryTypeId]
GO
ALTER TABLE [dbo].[BirthHistories]  WITH CHECK ADD  CONSTRAINT [FK_BirthHistories_GrowthCategories_GrowthCategoryId] FOREIGN KEY([GrowthCategoryId])
REFERENCES [dbo].[GrowthCategories] ([Id])
GO
ALTER TABLE [dbo].[BirthHistories] CHECK CONSTRAINT [FK_BirthHistories_GrowthCategories_GrowthCategoryId]
GO
ALTER TABLE [dbo].[BirthHistories]  WITH CHECK ADD  CONSTRAINT [FK_BirthHistories_Kids_Id] FOREIGN KEY([Id])
REFERENCES [dbo].[Kids] ([Id])
GO
ALTER TABLE [dbo].[BirthHistories] CHECK CONSTRAINT [FK_BirthHistories_Kids_Id]
GO
ALTER TABLE [dbo].[BirthLocationNameCustomOptions]  WITH CHECK ADD  CONSTRAINT [FK_BirthLocationNameCustomOptions_Subscribers_SubscriberId] FOREIGN KEY([SubscriberId])
REFERENCES [dbo].[Subscribers] ([Id])
GO
ALTER TABLE [dbo].[BirthLocationNameCustomOptions] CHECK CONSTRAINT [FK_BirthLocationNameCustomOptions_Subscribers_SubscriberId]
GO
ALTER TABLE [dbo].[BreastCustomOptions]  WITH CHECK ADD  CONSTRAINT [FK_BreastCustomOptions_Subscribers_SubscriberId] FOREIGN KEY([SubscriberId])
REFERENCES [dbo].[Subscribers] ([Id])
GO
ALTER TABLE [dbo].[BreastCustomOptions] CHECK CONSTRAINT [FK_BreastCustomOptions_Subscribers_SubscriberId]
GO
ALTER TABLE [dbo].[BreastFeedingHistories]  WITH CHECK ADD  CONSTRAINT [FK_BreastFeedingHistories_Kids_Id] FOREIGN KEY([Id])
REFERENCES [dbo].[Kids] ([Id])
GO
ALTER TABLE [dbo].[BreastFeedingHistories] CHECK CONSTRAINT [FK_BreastFeedingHistories_Kids_Id]
GO
ALTER TABLE [dbo].[BreastFeedingHistories]  WITH CHECK ADD  CONSTRAINT [FK_BreastFeedingHistories_Persons_ParentId] FOREIGN KEY([ParentId])
REFERENCES [dbo].[Persons] ([Id])
GO
ALTER TABLE [dbo].[BreastFeedingHistories] CHECK CONSTRAINT [FK_BreastFeedingHistories_Persons_ParentId]
GO
ALTER TABLE [dbo].[BreastObservations]  WITH CHECK ADD  CONSTRAINT [FK_BreastObservations_BreastCustomOptions_BreastCustomOptionId] FOREIGN KEY([BreastCustomOptionId])
REFERENCES [dbo].[BreastCustomOptions] ([Id])
GO
ALTER TABLE [dbo].[BreastObservations] CHECK CONSTRAINT [FK_BreastObservations_BreastCustomOptions_BreastCustomOptionId]
GO
ALTER TABLE [dbo].[BreastObservations]  WITH CHECK ADD  CONSTRAINT [FK_BreastObservations_BreastTypes_BreastTypeId] FOREIGN KEY([BreastTypeId])
REFERENCES [dbo].[BreastTypes] ([Id])
GO
ALTER TABLE [dbo].[BreastObservations] CHECK CONSTRAINT [FK_BreastObservations_BreastTypes_BreastTypeId]
GO
ALTER TABLE [dbo].[BreastObservations]  WITH CHECK ADD  CONSTRAINT [FK_BreastObservations_ParentEvaluations_EvaluationId] FOREIGN KEY([EvaluationId])
REFERENCES [dbo].[ParentEvaluations] ([Id])
GO
ALTER TABLE [dbo].[BreastObservations] CHECK CONSTRAINT [FK_BreastObservations_ParentEvaluations_EvaluationId]
GO
ALTER TABLE [dbo].[BreastPumpRentals]  WITH CHECK ADD  CONSTRAINT [FK_BreastPumpRentals_Persons_IssuedById] FOREIGN KEY([IssuedById])
REFERENCES [dbo].[Persons] ([Id])
GO
ALTER TABLE [dbo].[BreastPumpRentals] CHECK CONSTRAINT [FK_BreastPumpRentals_Persons_IssuedById]
GO
ALTER TABLE [dbo].[BreastPumpRentals]  WITH CHECK ADD  CONSTRAINT [FK_BreastPumpRentals_Persons_IssuedToId] FOREIGN KEY([IssuedToId])
REFERENCES [dbo].[Persons] ([Id])
GO
ALTER TABLE [dbo].[BreastPumpRentals] CHECK CONSTRAINT [FK_BreastPumpRentals_Persons_IssuedToId]
GO
ALTER TABLE [dbo].[BreastPumpRentals]  WITH CHECK ADD  CONSTRAINT [FK_BreastPumpRentals_SubscriberBreastPumps_SubscriberBreastPumpId] FOREIGN KEY([SubscriberBreastPumpId])
REFERENCES [dbo].[SubscriberBreastPumps] ([Id])
GO
ALTER TABLE [dbo].[BreastPumpRentals] CHECK CONSTRAINT [FK_BreastPumpRentals_SubscriberBreastPumps_SubscriberBreastPumpId]
GO
ALTER TABLE [dbo].[BreastPumps]  WITH CHECK ADD  CONSTRAINT [FK_BreastPumps_PumpBrandCustomOptions_PumpBrandCustomOptionId] FOREIGN KEY([PumpBrandCustomOptionId])
REFERENCES [dbo].[PumpBrandCustomOptions] ([Id])
GO
ALTER TABLE [dbo].[BreastPumps] CHECK CONSTRAINT [FK_BreastPumps_PumpBrandCustomOptions_PumpBrandCustomOptionId]
GO
ALTER TABLE [dbo].[BreastPumps]  WITH CHECK ADD  CONSTRAINT [FK_BreastPumps_PumpBrands_PumpBrandId] FOREIGN KEY([PumpBrandId])
REFERENCES [dbo].[PumpBrands] ([Id])
GO
ALTER TABLE [dbo].[BreastPumps] CHECK CONSTRAINT [FK_BreastPumps_PumpBrands_PumpBrandId]
GO
ALTER TABLE [dbo].[BreastPumps]  WITH CHECK ADD  CONSTRAINT [FK_BreastPumps_PumpTypes_PumpTypeId] FOREIGN KEY([PumpTypeId])
REFERENCES [dbo].[PumpTypes] ([Id])
GO
ALTER TABLE [dbo].[BreastPumps] CHECK CONSTRAINT [FK_BreastPumps_PumpTypes_PumpTypeId]
GO
ALTER TABLE [dbo].[BreastPumpServiceHistories]  WITH CHECK ADD  CONSTRAINT [FK_BreastPumpServiceHistories_SubscriberBreastPumps_SubscriberBreastPumpId] FOREIGN KEY([SubscriberBreastPumpId])
REFERENCES [dbo].[SubscriberBreastPumps] ([Id])
GO
ALTER TABLE [dbo].[BreastPumpServiceHistories] CHECK CONSTRAINT [FK_BreastPumpServiceHistories_SubscriberBreastPumps_SubscriberBreastPumpId]
GO
ALTER TABLE [dbo].[Calendars]  WITH CHECK ADD  CONSTRAINT [FK_Calendars_Images_ImageId] FOREIGN KEY([ImageId])
REFERENCES [dbo].[Images] ([Id])
GO
ALTER TABLE [dbo].[Calendars] CHECK CONSTRAINT [FK_Calendars_Images_ImageId]
GO
ALTER TABLE [dbo].[CarePlanCustomInstructions]  WITH CHECK ADD  CONSTRAINT [FK_CarePlanCustomInstructions_CarePlans_CarePlanId] FOREIGN KEY([CarePlanId])
REFERENCES [dbo].[CarePlans] ([Id])
GO
ALTER TABLE [dbo].[CarePlanCustomInstructions] CHECK CONSTRAINT [FK_CarePlanCustomInstructions_CarePlans_CarePlanId]
GO
ALTER TABLE [dbo].[CarePlanCustomInstructions]  WITH CHECK ADD  CONSTRAINT [FK_CarePlanCustomInstructions_CustomInstructions_CustomInstructionId] FOREIGN KEY([CustomInstructionId])
REFERENCES [dbo].[CustomInstructions] ([Id])
GO
ALTER TABLE [dbo].[CarePlanCustomInstructions] CHECK CONSTRAINT [FK_CarePlanCustomInstructions_CustomInstructions_CustomInstructionId]
GO
ALTER TABLE [dbo].[CarePlanDocumentResources]  WITH CHECK ADD  CONSTRAINT [FK_CarePlanDocumentResources_CarePlans_CarePlanId] FOREIGN KEY([CarePlanId])
REFERENCES [dbo].[CarePlans] ([Id])
GO
ALTER TABLE [dbo].[CarePlanDocumentResources] CHECK CONSTRAINT [FK_CarePlanDocumentResources_CarePlans_CarePlanId]
GO
ALTER TABLE [dbo].[CarePlanDocumentResources]  WITH CHECK ADD  CONSTRAINT [FK_CarePlanDocumentResources_DocumentResources_DocumentResourceId] FOREIGN KEY([DocumentResourceId])
REFERENCES [dbo].[DocumentResources] ([Id])
GO
ALTER TABLE [dbo].[CarePlanDocumentResources] CHECK CONSTRAINT [FK_CarePlanDocumentResources_DocumentResources_DocumentResourceId]
GO
ALTER TABLE [dbo].[CarePlanReferralProviders]  WITH CHECK ADD  CONSTRAINT [FK_CarePlanReferralProviders_CarePlans_CarePlanId] FOREIGN KEY([CarePlanId])
REFERENCES [dbo].[CarePlans] ([Id])
GO
ALTER TABLE [dbo].[CarePlanReferralProviders] CHECK CONSTRAINT [FK_CarePlanReferralProviders_CarePlans_CarePlanId]
GO
ALTER TABLE [dbo].[CarePlanReferralProviders]  WITH CHECK ADD  CONSTRAINT [FK_CarePlanReferralProviders_SubscriberProviders_SubscriberProviderId] FOREIGN KEY([SubscriberProviderId])
REFERENCES [dbo].[SubscriberProviders] ([Id])
GO
ALTER TABLE [dbo].[CarePlanReferralProviders] CHECK CONSTRAINT [FK_CarePlanReferralProviders_SubscriberProviders_SubscriberProviderId]
GO
ALTER TABLE [dbo].[CarePlans]  WITH CHECK ADD  CONSTRAINT [FK_CarePlans_Evaluations_Id] FOREIGN KEY([Id])
REFERENCES [dbo].[Evaluations] ([Id])
GO
ALTER TABLE [dbo].[CarePlans] CHECK CONSTRAINT [FK_CarePlans_Evaluations_Id]
GO
ALTER TABLE [dbo].[ChildProfileNotes]  WITH CHECK ADD  CONSTRAINT [FK_ChildProfileNotes_Persons_ChildId] FOREIGN KEY([ChildId])
REFERENCES [dbo].[Persons] ([Id])
GO
ALTER TABLE [dbo].[ChildProfileNotes] CHECK CONSTRAINT [FK_ChildProfileNotes_Persons_ChildId]
GO
ALTER TABLE [dbo].[ChildProfileNotes]  WITH CHECK ADD  CONSTRAINT [FK_ChildProfileNotes_Visits_VisitId] FOREIGN KEY([VisitId])
REFERENCES [dbo].[Visits] ([Id])
GO
ALTER TABLE [dbo].[ChildProfileNotes] CHECK CONSTRAINT [FK_ChildProfileNotes_Visits_VisitId]
GO
ALTER TABLE [dbo].[ChildStateCustomOptions]  WITH CHECK ADD  CONSTRAINT [FK_ChildStateCustomOptions_Subscribers_SubscriberId] FOREIGN KEY([SubscriberId])
REFERENCES [dbo].[Subscribers] ([Id])
GO
ALTER TABLE [dbo].[ChildStateCustomOptions] CHECK CONSTRAINT [FK_ChildStateCustomOptions_Subscribers_SubscriberId]
GO
ALTER TABLE [dbo].[ChildStates]  WITH CHECK ADD  CONSTRAINT [FK_ChildStates_ChildStateCustomOptions_ChildStateCustomOptionId] FOREIGN KEY([ChildStateCustomOptionId])
REFERENCES [dbo].[ChildStateCustomOptions] ([Id])
GO
ALTER TABLE [dbo].[ChildStates] CHECK CONSTRAINT [FK_ChildStates_ChildStateCustomOptions_ChildStateCustomOptionId]
GO
ALTER TABLE [dbo].[ChildStates]  WITH CHECK ADD  CONSTRAINT [FK_ChildStates_ChildStateTypes_ChildStateTypeId] FOREIGN KEY([ChildStateTypeId])
REFERENCES [dbo].[ChildStateTypes] ([Id])
GO
ALTER TABLE [dbo].[ChildStates] CHECK CONSTRAINT [FK_ChildStates_ChildStateTypes_ChildStateTypeId]
GO
ALTER TABLE [dbo].[ChildStates]  WITH CHECK ADD  CONSTRAINT [FK_ChildStates_KidEvaluations_EvaluationId] FOREIGN KEY([EvaluationId])
REFERENCES [dbo].[KidEvaluations] ([Id])
GO
ALTER TABLE [dbo].[ChildStates] CHECK CONSTRAINT [FK_ChildStates_KidEvaluations_EvaluationId]
GO
ALTER TABLE [dbo].[ComplicationCustomOptions]  WITH CHECK ADD  CONSTRAINT [FK_ComplicationCustomOptions_Subscribers_SubscriberId] FOREIGN KEY([SubscriberId])
REFERENCES [dbo].[Subscribers] ([Id])
GO
ALTER TABLE [dbo].[ComplicationCustomOptions] CHECK CONSTRAINT [FK_ComplicationCustomOptions_Subscribers_SubscriberId]
GO
ALTER TABLE [dbo].[Complications]  WITH CHECK ADD  CONSTRAINT [FK_Complications_BirthHistories_BirthHistoryId] FOREIGN KEY([BirthHistoryId])
REFERENCES [dbo].[BirthHistories] ([Id])
GO
ALTER TABLE [dbo].[Complications] CHECK CONSTRAINT [FK_Complications_BirthHistories_BirthHistoryId]
GO
ALTER TABLE [dbo].[Complications]  WITH CHECK ADD  CONSTRAINT [FK_Complications_ComplicationCustomOptions_ComplicationCustomOptionId] FOREIGN KEY([ComplicationCustomOptionId])
REFERENCES [dbo].[ComplicationCustomOptions] ([Id])
GO
ALTER TABLE [dbo].[Complications] CHECK CONSTRAINT [FK_Complications_ComplicationCustomOptions_ComplicationCustomOptionId]
GO
ALTER TABLE [dbo].[Complications]  WITH CHECK ADD  CONSTRAINT [FK_Complications_ComplicationTypes_ComplicationTypeId] FOREIGN KEY([ComplicationTypeId])
REFERENCES [dbo].[ComplicationTypes] ([Id])
GO
ALTER TABLE [dbo].[Complications] CHECK CONSTRAINT [FK_Complications_ComplicationTypes_ComplicationTypeId]
GO
ALTER TABLE [dbo].[ConcernCustomOptions]  WITH CHECK ADD  CONSTRAINT [FK_ConcernCustomOptions_Subscribers_SubscriberId] FOREIGN KEY([SubscriberId])
REFERENCES [dbo].[Subscribers] ([Id])
GO
ALTER TABLE [dbo].[ConcernCustomOptions] CHECK CONSTRAINT [FK_ConcernCustomOptions_Subscribers_SubscriberId]
GO
ALTER TABLE [dbo].[CustomInstructions]  WITH CHECK ADD  CONSTRAINT [FK_CustomInstructions_GoalTypes_GoalTypeId] FOREIGN KEY([GoalTypeId])
REFERENCES [dbo].[GoalTypes] ([Id])
GO
ALTER TABLE [dbo].[CustomInstructions] CHECK CONSTRAINT [FK_CustomInstructions_GoalTypes_GoalTypeId]
GO
ALTER TABLE [dbo].[CustomInstructions]  WITH CHECK ADD  CONSTRAINT [FK_CustomInstructions_Subscribers_SubscriberId] FOREIGN KEY([SubscriberId])
REFERENCES [dbo].[Subscribers] ([Id])
GO
ALTER TABLE [dbo].[CustomInstructions] CHECK CONSTRAINT [FK_CustomInstructions_Subscribers_SubscriberId]
GO
ALTER TABLE [dbo].[DailyAvailabilities]  WITH CHECK ADD  CONSTRAINT [FK_DailyAvailabilities_SubscriberUsers_SubscriberUserId] FOREIGN KEY([SubscriberUserId])
REFERENCES [dbo].[SubscriberUsers] ([Id])
GO
ALTER TABLE [dbo].[DailyAvailabilities] CHECK CONSTRAINT [FK_DailyAvailabilities_SubscriberUsers_SubscriberUserId]
GO
ALTER TABLE [dbo].[DailyAvailabilityAppointmentTypes]  WITH CHECK ADD  CONSTRAINT [FK_DailyAvailabilityAppointmentTypes_AppointmentTypes_AppointmentTypeId] FOREIGN KEY([AppointmentTypeId])
REFERENCES [dbo].[AppointmentTypes] ([Id])
GO
ALTER TABLE [dbo].[DailyAvailabilityAppointmentTypes] CHECK CONSTRAINT [FK_DailyAvailabilityAppointmentTypes_AppointmentTypes_AppointmentTypeId]
GO
ALTER TABLE [dbo].[DailyAvailabilityAppointmentTypes]  WITH CHECK ADD  CONSTRAINT [FK_DailyAvailabilityAppointmentTypes_DailyAvailabilities_DailyAvailabilityId] FOREIGN KEY([DailyAvailabilityId])
REFERENCES [dbo].[DailyAvailabilities] ([Id])
GO
ALTER TABLE [dbo].[DailyAvailabilityAppointmentTypes] CHECK CONSTRAINT [FK_DailyAvailabilityAppointmentTypes_DailyAvailabilities_DailyAvailabilityId]
GO
ALTER TABLE [dbo].[Diagnoses]  WITH CHECK ADD  CONSTRAINT [FK_Diagnoses_DiagnosisTypeCategorizedCustomOptions_DiagnosisTypeCategorizedCustomOptionId] FOREIGN KEY([DiagnosisTypeCategorizedCustomOptionId])
REFERENCES [dbo].[DiagnosisTypeCategorizedCustomOptions] ([Id])
GO
ALTER TABLE [dbo].[Diagnoses] CHECK CONSTRAINT [FK_Diagnoses_DiagnosisTypeCategorizedCustomOptions_DiagnosisTypeCategorizedCustomOptionId]
GO
ALTER TABLE [dbo].[Diagnoses]  WITH CHECK ADD  CONSTRAINT [FK_Diagnoses_DiagnosisTypes_DiagnosisTypeId] FOREIGN KEY([DiagnosisTypeId])
REFERENCES [dbo].[DiagnosisTypes] ([Id])
GO
ALTER TABLE [dbo].[Diagnoses] CHECK CONSTRAINT [FK_Diagnoses_DiagnosisTypes_DiagnosisTypeId]
GO
ALTER TABLE [dbo].[Diagnoses]  WITH CHECK ADD  CONSTRAINT [FK_Diagnoses_HumanSystems_HumanSystemId] FOREIGN KEY([HumanSystemId])
REFERENCES [dbo].[HumanSystems] ([Id])
GO
ALTER TABLE [dbo].[Diagnoses] CHECK CONSTRAINT [FK_Diagnoses_HumanSystems_HumanSystemId]
GO
ALTER TABLE [dbo].[Diagnoses]  WITH CHECK ADD  CONSTRAINT [FK_Diagnoses_Persons_PersonId] FOREIGN KEY([PersonId])
REFERENCES [dbo].[Persons] ([Id])
GO
ALTER TABLE [dbo].[Diagnoses] CHECK CONSTRAINT [FK_Diagnoses_Persons_PersonId]
GO
ALTER TABLE [dbo].[Diagnoses]  WITH CHECK ADD  CONSTRAINT [FK_Diagnoses_Visits_VisitId] FOREIGN KEY([VisitId])
REFERENCES [dbo].[Visits] ([Id])
GO
ALTER TABLE [dbo].[Diagnoses] CHECK CONSTRAINT [FK_Diagnoses_Visits_VisitId]
GO
ALTER TABLE [dbo].[DiagnosisTypeCategorizedCustomOptions]  WITH CHECK ADD  CONSTRAINT [FK_DiagnosisTypeCategorizedCustomOptions_HumanSystems_ParentValueId] FOREIGN KEY([ParentValueId])
REFERENCES [dbo].[HumanSystems] ([Id])
GO
ALTER TABLE [dbo].[DiagnosisTypeCategorizedCustomOptions] CHECK CONSTRAINT [FK_DiagnosisTypeCategorizedCustomOptions_HumanSystems_ParentValueId]
GO
ALTER TABLE [dbo].[DiagnosisTypeCategorizedCustomOptions]  WITH CHECK ADD  CONSTRAINT [FK_DiagnosisTypeCategorizedCustomOptions_Subscribers_SubscriberId] FOREIGN KEY([SubscriberId])
REFERENCES [dbo].[Subscribers] ([Id])
GO
ALTER TABLE [dbo].[DiagnosisTypeCategorizedCustomOptions] CHECK CONSTRAINT [FK_DiagnosisTypeCategorizedCustomOptions_Subscribers_SubscriberId]
GO
ALTER TABLE [dbo].[DiagnosisTypes]  WITH CHECK ADD  CONSTRAINT [FK_DiagnosisTypes_HumanSystems_HumanSystemId] FOREIGN KEY([HumanSystemId])
REFERENCES [dbo].[HumanSystems] ([Id])
GO
ALTER TABLE [dbo].[DiagnosisTypes] CHECK CONSTRAINT [FK_DiagnosisTypes_HumanSystems_HumanSystemId]
GO
ALTER TABLE [dbo].[DocumentResources]  WITH CHECK ADD  CONSTRAINT [FK_DocumentResources_DocumentResources_ParentDocumentResourceId] FOREIGN KEY([ParentDocumentResourceId])
REFERENCES [dbo].[DocumentResources] ([Id])
GO
ALTER TABLE [dbo].[DocumentResources] CHECK CONSTRAINT [FK_DocumentResources_DocumentResources_ParentDocumentResourceId]
GO
ALTER TABLE [dbo].[DocumentResources]  WITH CHECK ADD  CONSTRAINT [FK_DocumentResources_Subscribers_SubscriberId] FOREIGN KEY([SubscriberId])
REFERENCES [dbo].[Subscribers] ([Id])
GO
ALTER TABLE [dbo].[DocumentResources] CHECK CONSTRAINT [FK_DocumentResources_Subscribers_SubscriberId]
GO
ALTER TABLE [dbo].[Emails]  WITH CHECK ADD  CONSTRAINT [FK_Emails_EmailTypes_EmailTypeId] FOREIGN KEY([EmailTypeId])
REFERENCES [dbo].[EmailTypes] ([Id])
GO
ALTER TABLE [dbo].[Emails] CHECK CONSTRAINT [FK_Emails_EmailTypes_EmailTypeId]
GO
ALTER TABLE [dbo].[Evaluations]  WITH CHECK ADD  CONSTRAINT [FK_Evaluations_Persons_ChildId] FOREIGN KEY([ChildId])
REFERENCES [dbo].[Persons] ([Id])
GO
ALTER TABLE [dbo].[Evaluations] CHECK CONSTRAINT [FK_Evaluations_Persons_ChildId]
GO
ALTER TABLE [dbo].[Evaluations]  WITH CHECK ADD  CONSTRAINT [FK_Evaluations_Visits_VisitId] FOREIGN KEY([VisitId])
REFERENCES [dbo].[Visits] ([Id])
GO
ALTER TABLE [dbo].[Evaluations] CHECK CONSTRAINT [FK_Evaluations_Visits_VisitId]
GO
ALTER TABLE [dbo].[FeedingMethodCustomOptions]  WITH CHECK ADD  CONSTRAINT [FK_FeedingMethodCustomOptions_Subscribers_SubscriberId] FOREIGN KEY([SubscriberId])
REFERENCES [dbo].[Subscribers] ([Id])
GO
ALTER TABLE [dbo].[FeedingMethodCustomOptions] CHECK CONSTRAINT [FK_FeedingMethodCustomOptions_Subscribers_SubscriberId]
GO
ALTER TABLE [dbo].[FeedingMethods]  WITH CHECK ADD  CONSTRAINT [FK_FeedingMethods_FeedingMethodCustomOptions_FeedingMethodCustomOptionId] FOREIGN KEY([FeedingMethodCustomOptionId])
REFERENCES [dbo].[FeedingMethodCustomOptions] ([Id])
GO
ALTER TABLE [dbo].[FeedingMethods] CHECK CONSTRAINT [FK_FeedingMethods_FeedingMethodCustomOptions_FeedingMethodCustomOptionId]
GO
ALTER TABLE [dbo].[FeedingMethods]  WITH CHECK ADD  CONSTRAINT [FK_FeedingMethods_FeedingMethodTypes_FeedingMethodTypeId] FOREIGN KEY([FeedingMethodTypeId])
REFERENCES [dbo].[FeedingMethodTypes] ([Id])
GO
ALTER TABLE [dbo].[FeedingMethods] CHECK CONSTRAINT [FK_FeedingMethods_FeedingMethodTypes_FeedingMethodTypeId]
GO
ALTER TABLE [dbo].[FeedingMethods]  WITH CHECK ADD  CONSTRAINT [FK_FeedingMethods_KidEvaluations_EvaluationId] FOREIGN KEY([EvaluationId])
REFERENCES [dbo].[KidEvaluations] ([Id])
GO
ALTER TABLE [dbo].[FeedingMethods] CHECK CONSTRAINT [FK_FeedingMethods_KidEvaluations_EvaluationId]
GO
ALTER TABLE [dbo].[FeedingPositionCustomOptions]  WITH CHECK ADD  CONSTRAINT [FK_FeedingPositionCustomOptions_Subscribers_SubscriberId] FOREIGN KEY([SubscriberId])
REFERENCES [dbo].[Subscribers] ([Id])
GO
ALTER TABLE [dbo].[FeedingPositionCustomOptions] CHECK CONSTRAINT [FK_FeedingPositionCustomOptions_Subscribers_SubscriberId]
GO
ALTER TABLE [dbo].[FeedingPositions]  WITH CHECK ADD  CONSTRAINT [FK_FeedingPositions_FeedingPositionCustomOptions_FeedingPositionCustomOptionId] FOREIGN KEY([FeedingPositionCustomOptionId])
REFERENCES [dbo].[FeedingPositionCustomOptions] ([Id])
GO
ALTER TABLE [dbo].[FeedingPositions] CHECK CONSTRAINT [FK_FeedingPositions_FeedingPositionCustomOptions_FeedingPositionCustomOptionId]
GO
ALTER TABLE [dbo].[FeedingPositions]  WITH CHECK ADD  CONSTRAINT [FK_FeedingPositions_FeedingPositionTypes_FeedingPositionTypeId] FOREIGN KEY([FeedingPositionTypeId])
REFERENCES [dbo].[FeedingPositionTypes] ([Id])
GO
ALTER TABLE [dbo].[FeedingPositions] CHECK CONSTRAINT [FK_FeedingPositions_FeedingPositionTypes_FeedingPositionTypeId]
GO
ALTER TABLE [dbo].[FeedingPositions]  WITH CHECK ADD  CONSTRAINT [FK_FeedingPositions_KidEvaluations_EvaluationId] FOREIGN KEY([EvaluationId])
REFERENCES [dbo].[KidEvaluations] ([Id])
GO
ALTER TABLE [dbo].[FeedingPositions] CHECK CONSTRAINT [FK_FeedingPositions_KidEvaluations_EvaluationId]
GO
ALTER TABLE [dbo].[FlangeSizeCustomOptions]  WITH CHECK ADD  CONSTRAINT [FK_FlangeSizeCustomOptions_Subscribers_SubscriberId] FOREIGN KEY([SubscriberId])
REFERENCES [dbo].[Subscribers] ([Id])
GO
ALTER TABLE [dbo].[FlangeSizeCustomOptions] CHECK CONSTRAINT [FK_FlangeSizeCustomOptions_Subscribers_SubscriberId]
GO
ALTER TABLE [dbo].[Goals]  WITH CHECK ADD  CONSTRAINT [FK_Goals_CarePlans_EvaluationId] FOREIGN KEY([EvaluationId])
REFERENCES [dbo].[CarePlans] ([Id])
GO
ALTER TABLE [dbo].[Goals] CHECK CONSTRAINT [FK_Goals_CarePlans_EvaluationId]
GO
ALTER TABLE [dbo].[Goals]  WITH CHECK ADD  CONSTRAINT [FK_Goals_GoalTypes_GoalTypeId] FOREIGN KEY([GoalTypeId])
REFERENCES [dbo].[GoalTypes] ([Id])
GO
ALTER TABLE [dbo].[Goals] CHECK CONSTRAINT [FK_Goals_GoalTypes_GoalTypeId]
GO
ALTER TABLE [dbo].[ICD10CodeCustomOptions]  WITH CHECK ADD  CONSTRAINT [FK_ICD10CodeCustomOptions_Subscribers_SubscriberId] FOREIGN KEY([SubscriberId])
REFERENCES [dbo].[Subscribers] ([Id])
GO
ALTER TABLE [dbo].[ICD10CodeCustomOptions] CHECK CONSTRAINT [FK_ICD10CodeCustomOptions_Subscribers_SubscriberId]
GO
ALTER TABLE [dbo].[ICD10s]  WITH CHECK ADD  CONSTRAINT [FK_ICD10s_ICD10CodeCustomOptions_ICD10CodeCustomOptionId] FOREIGN KEY([ICD10CodeCustomOptionId])
REFERENCES [dbo].[ICD10CodeCustomOptions] ([Id])
GO
ALTER TABLE [dbo].[ICD10s] CHECK CONSTRAINT [FK_ICD10s_ICD10CodeCustomOptions_ICD10CodeCustomOptionId]
GO
ALTER TABLE [dbo].[ICD10s]  WITH CHECK ADD  CONSTRAINT [FK_ICD10s_ICD10Codes_ICD10CodeId] FOREIGN KEY([ICD10CodeId])
REFERENCES [dbo].[ICD10Codes] ([Id])
GO
ALTER TABLE [dbo].[ICD10s] CHECK CONSTRAINT [FK_ICD10s_ICD10Codes_ICD10CodeId]
GO
ALTER TABLE [dbo].[ICD10s]  WITH CHECK ADD  CONSTRAINT [FK_ICD10s_Persons_PersonId] FOREIGN KEY([PersonId])
REFERENCES [dbo].[Persons] ([Id])
GO
ALTER TABLE [dbo].[ICD10s] CHECK CONSTRAINT [FK_ICD10s_Persons_PersonId]
GO
ALTER TABLE [dbo].[ICD10s]  WITH CHECK ADD  CONSTRAINT [FK_ICD10s_Visits_VisitId] FOREIGN KEY([VisitId])
REFERENCES [dbo].[Visits] ([Id])
GO
ALTER TABLE [dbo].[ICD10s] CHECK CONSTRAINT [FK_ICD10s_Visits_VisitId]
GO
ALTER TABLE [dbo].[Instructions]  WITH CHECK ADD  CONSTRAINT [FK_Instructions_CarePlans_EvaluationId] FOREIGN KEY([EvaluationId])
REFERENCES [dbo].[CarePlans] ([Id])
GO
ALTER TABLE [dbo].[Instructions] CHECK CONSTRAINT [FK_Instructions_CarePlans_EvaluationId]
GO
ALTER TABLE [dbo].[Instructions]  WITH CHECK ADD  CONSTRAINT [FK_Instructions_InstructionTypes_InstructionTypeId] FOREIGN KEY([InstructionTypeId])
REFERENCES [dbo].[InstructionTypes] ([Id])
GO
ALTER TABLE [dbo].[Instructions] CHECK CONSTRAINT [FK_Instructions_InstructionTypes_InstructionTypeId]
GO
ALTER TABLE [dbo].[InsurancePolicies]  WITH CHECK ADD  CONSTRAINT [FK_InsurancePolicies_Persons_PersonId] FOREIGN KEY([PersonId])
REFERENCES [dbo].[Persons] ([Id])
GO
ALTER TABLE [dbo].[InsurancePolicies] CHECK CONSTRAINT [FK_InsurancePolicies_Persons_PersonId]
GO
ALTER TABLE [dbo].[InsurancePolicies]  WITH CHECK ADD  CONSTRAINT [FK_InsurancePolicies_Persons_PolicyHolderId] FOREIGN KEY([PolicyHolderId])
REFERENCES [dbo].[Persons] ([Id])
GO
ALTER TABLE [dbo].[InsurancePolicies] CHECK CONSTRAINT [FK_InsurancePolicies_Persons_PolicyHolderId]
GO
ALTER TABLE [dbo].[InsurancePolicies]  WITH CHECK ADD  CONSTRAINT [FK_InsurancePolicies_PolicyInsuranceCompanies_PolicyInsuranceCompanyId] FOREIGN KEY([PolicyInsuranceCompanyId])
REFERENCES [dbo].[PolicyInsuranceCompanies] ([Id])
GO
ALTER TABLE [dbo].[InsurancePolicies] CHECK CONSTRAINT [FK_InsurancePolicies_PolicyInsuranceCompanies_PolicyInsuranceCompanyId]
GO
ALTER TABLE [dbo].[InsurancePolicyImages]  WITH CHECK ADD  CONSTRAINT [FK_InsurancePolicyImages_Images_ImageId] FOREIGN KEY([ImageId])
REFERENCES [dbo].[Images] ([Id])
GO
ALTER TABLE [dbo].[InsurancePolicyImages] CHECK CONSTRAINT [FK_InsurancePolicyImages_Images_ImageId]
GO
ALTER TABLE [dbo].[InsurancePolicyImages]  WITH CHECK ADD  CONSTRAINT [FK_InsurancePolicyImages_InsurancePolicies_InsurancePolicyId] FOREIGN KEY([InsurancePolicyId])
REFERENCES [dbo].[InsurancePolicies] ([Id])
GO
ALTER TABLE [dbo].[InsurancePolicyImages] CHECK CONSTRAINT [FK_InsurancePolicyImages_InsurancePolicies_InsurancePolicyId]
GO
ALTER TABLE [dbo].[InterventionCustomOptions]  WITH CHECK ADD  CONSTRAINT [FK_InterventionCustomOptions_Subscribers_SubscriberId] FOREIGN KEY([SubscriberId])
REFERENCES [dbo].[Subscribers] ([Id])
GO
ALTER TABLE [dbo].[InterventionCustomOptions] CHECK CONSTRAINT [FK_InterventionCustomOptions_Subscribers_SubscriberId]
GO
ALTER TABLE [dbo].[Interventions]  WITH CHECK ADD  CONSTRAINT [FK_Interventions_BirthHistories_BirthHistoryId] FOREIGN KEY([BirthHistoryId])
REFERENCES [dbo].[BirthHistories] ([Id])
GO
ALTER TABLE [dbo].[Interventions] CHECK CONSTRAINT [FK_Interventions_BirthHistories_BirthHistoryId]
GO
ALTER TABLE [dbo].[Interventions]  WITH CHECK ADD  CONSTRAINT [FK_Interventions_InterventionCustomOptions_InterventionCustomOptionId] FOREIGN KEY([InterventionCustomOptionId])
REFERENCES [dbo].[InterventionCustomOptions] ([Id])
GO
ALTER TABLE [dbo].[Interventions] CHECK CONSTRAINT [FK_Interventions_InterventionCustomOptions_InterventionCustomOptionId]
GO
ALTER TABLE [dbo].[Interventions]  WITH CHECK ADD  CONSTRAINT [FK_Interventions_InterventionTypes_InterventionTypeId] FOREIGN KEY([InterventionTypeId])
REFERENCES [dbo].[InterventionTypes] ([Id])
GO
ALTER TABLE [dbo].[Interventions] CHECK CONSTRAINT [FK_Interventions_InterventionTypes_InterventionTypeId]
GO
ALTER TABLE [dbo].[KidEvaluations]  WITH CHECK ADD  CONSTRAINT [FK_KidEvaluations_Evaluations_Id] FOREIGN KEY([Id])
REFERENCES [dbo].[Evaluations] ([Id])
GO
ALTER TABLE [dbo].[KidEvaluations] CHECK CONSTRAINT [FK_KidEvaluations_Evaluations_Id]
GO
ALTER TABLE [dbo].[KidEvaluations]  WITH CHECK ADD  CONSTRAINT [FK_KidEvaluations_SkinTurgor_SkinTurgorId] FOREIGN KEY([SkinTurgorId])
REFERENCES [dbo].[SkinTurgor] ([Id])
GO
ALTER TABLE [dbo].[KidEvaluations] CHECK CONSTRAINT [FK_KidEvaluations_SkinTurgor_SkinTurgorId]
GO
ALTER TABLE [dbo].[Kids]  WITH CHECK ADD  CONSTRAINT [FK_Kids_Persons_ChildId] FOREIGN KEY([ChildId])
REFERENCES [dbo].[Persons] ([Id])
GO
ALTER TABLE [dbo].[Kids] CHECK CONSTRAINT [FK_Kids_Persons_ChildId]
GO
ALTER TABLE [dbo].[Kids]  WITH CHECK ADD  CONSTRAINT [FK_Kids_Persons_ParentId] FOREIGN KEY([ParentId])
REFERENCES [dbo].[Persons] ([Id])
GO
ALTER TABLE [dbo].[Kids] CHECK CONSTRAINT [FK_Kids_Persons_ParentId]
GO
ALTER TABLE [dbo].[LactationEvaluationCustomOptions]  WITH CHECK ADD  CONSTRAINT [FK_LactationEvaluationCustomOptions_Subscribers_SubscriberId] FOREIGN KEY([SubscriberId])
REFERENCES [dbo].[Subscribers] ([Id])
GO
ALTER TABLE [dbo].[LactationEvaluationCustomOptions] CHECK CONSTRAINT [FK_LactationEvaluationCustomOptions_Subscribers_SubscriberId]
GO
ALTER TABLE [dbo].[LactationEvaluations]  WITH CHECK ADD  CONSTRAINT [FK_LactationEvaluations_KidEvaluations_EvaluationId] FOREIGN KEY([EvaluationId])
REFERENCES [dbo].[KidEvaluations] ([Id])
GO
ALTER TABLE [dbo].[LactationEvaluations] CHECK CONSTRAINT [FK_LactationEvaluations_KidEvaluations_EvaluationId]
GO
ALTER TABLE [dbo].[LactationEvaluations]  WITH CHECK ADD  CONSTRAINT [FK_LactationEvaluations_LactationEvaluationCustomOptions_LactationEvaluationCustomOptionId] FOREIGN KEY([LactationEvaluationCustomOptionId])
REFERENCES [dbo].[LactationEvaluationCustomOptions] ([Id])
GO
ALTER TABLE [dbo].[LactationEvaluations] CHECK CONSTRAINT [FK_LactationEvaluations_LactationEvaluationCustomOptions_LactationEvaluationCustomOptionId]
GO
ALTER TABLE [dbo].[LactationEvaluations]  WITH CHECK ADD  CONSTRAINT [FK_LactationEvaluations_LactationEvaluationTypes_EvaluationTypeId] FOREIGN KEY([EvaluationTypeId])
REFERENCES [dbo].[LactationEvaluationTypes] ([Id])
GO
ALTER TABLE [dbo].[LactationEvaluations] CHECK CONSTRAINT [FK_LactationEvaluations_LactationEvaluationTypes_EvaluationTypeId]
GO
ALTER TABLE [dbo].[LipCustomOptions]  WITH CHECK ADD  CONSTRAINT [FK_LipCustomOptions_Subscribers_SubscriberId] FOREIGN KEY([SubscriberId])
REFERENCES [dbo].[Subscribers] ([Id])
GO
ALTER TABLE [dbo].[LipCustomOptions] CHECK CONSTRAINT [FK_LipCustomOptions_Subscribers_SubscriberId]
GO
ALTER TABLE [dbo].[LipObservations]  WITH CHECK ADD  CONSTRAINT [FK_LipObservations_KidEvaluations_EvaluationId] FOREIGN KEY([EvaluationId])
REFERENCES [dbo].[KidEvaluations] ([Id])
GO
ALTER TABLE [dbo].[LipObservations] CHECK CONSTRAINT [FK_LipObservations_KidEvaluations_EvaluationId]
GO
ALTER TABLE [dbo].[LipObservations]  WITH CHECK ADD  CONSTRAINT [FK_LipObservations_LipCustomOptions_LipCustomOptionId] FOREIGN KEY([LipCustomOptionId])
REFERENCES [dbo].[LipCustomOptions] ([Id])
GO
ALTER TABLE [dbo].[LipObservations] CHECK CONSTRAINT [FK_LipObservations_LipCustomOptions_LipCustomOptionId]
GO
ALTER TABLE [dbo].[LipObservations]  WITH CHECK ADD  CONSTRAINT [FK_LipObservations_LipTypes_LipTypeId] FOREIGN KEY([LipTypeId])
REFERENCES [dbo].[LipTypes] ([Id])
GO
ALTER TABLE [dbo].[LipObservations] CHECK CONSTRAINT [FK_LipObservations_LipTypes_LipTypeId]
GO
ALTER TABLE [dbo].[MeasuredWeights]  WITH CHECK ADD  CONSTRAINT [FK_MeasuredWeights_Persons_PersonId] FOREIGN KEY([PersonId])
REFERENCES [dbo].[Persons] ([Id])
GO
ALTER TABLE [dbo].[MeasuredWeights] CHECK CONSTRAINT [FK_MeasuredWeights_Persons_PersonId]
GO
ALTER TABLE [dbo].[MeasuredWeights]  WITH CHECK ADD  CONSTRAINT [FK_MeasuredWeights_Visits_VisitId] FOREIGN KEY([VisitId])
REFERENCES [dbo].[Visits] ([Id])
GO
ALTER TABLE [dbo].[MeasuredWeights] CHECK CONSTRAINT [FK_MeasuredWeights_Visits_VisitId]
GO
ALTER TABLE [dbo].[MeasuredWeights]  WITH CHECK ADD  CONSTRAINT [FK_MeasuredWeights_WeightTypes_WeightTypeId] FOREIGN KEY([WeightTypeId])
REFERENCES [dbo].[WeightTypes] ([Id])
GO
ALTER TABLE [dbo].[MeasuredWeights] CHECK CONSTRAINT [FK_MeasuredWeights_WeightTypes_WeightTypeId]
GO
ALTER TABLE [dbo].[MeasuredWeights]  WITH CHECK ADD  CONSTRAINT [FK_MeasuredWeights_WeightUnits_WeightUnitId] FOREIGN KEY([WeightUnitId])
REFERENCES [dbo].[WeightUnits] ([Id])
GO
ALTER TABLE [dbo].[MeasuredWeights] CHECK CONSTRAINT [FK_MeasuredWeights_WeightUnits_WeightUnitId]
GO
ALTER TABLE [dbo].[MedicationCustomOptions]  WITH CHECK ADD  CONSTRAINT [FK_MedicationCustomOptions_Subscribers_SubscriberId] FOREIGN KEY([SubscriberId])
REFERENCES [dbo].[Subscribers] ([Id])
GO
ALTER TABLE [dbo].[MedicationCustomOptions] CHECK CONSTRAINT [FK_MedicationCustomOptions_Subscribers_SubscriberId]
GO
ALTER TABLE [dbo].[Medications]  WITH CHECK ADD  CONSTRAINT [FK_Medications_MedicationCustomOptions_MedicationCustomOptionId] FOREIGN KEY([MedicationCustomOptionId])
REFERENCES [dbo].[MedicationCustomOptions] ([Id])
GO
ALTER TABLE [dbo].[Medications] CHECK CONSTRAINT [FK_Medications_MedicationCustomOptions_MedicationCustomOptionId]
GO
ALTER TABLE [dbo].[Medications]  WITH CHECK ADD  CONSTRAINT [FK_Medications_MedicationTypes_MedicationTypeId] FOREIGN KEY([MedicationTypeId])
REFERENCES [dbo].[MedicationTypes] ([Id])
GO
ALTER TABLE [dbo].[Medications] CHECK CONSTRAINT [FK_Medications_MedicationTypes_MedicationTypeId]
GO
ALTER TABLE [dbo].[Medications]  WITH CHECK ADD  CONSTRAINT [FK_Medications_Persons_PersonId] FOREIGN KEY([PersonId])
REFERENCES [dbo].[Persons] ([Id])
GO
ALTER TABLE [dbo].[Medications] CHECK CONSTRAINT [FK_Medications_Persons_PersonId]
GO
ALTER TABLE [dbo].[Medications]  WITH CHECK ADD  CONSTRAINT [FK_Medications_Visits_VisitId] FOREIGN KEY([VisitId])
REFERENCES [dbo].[Visits] ([Id])
GO
ALTER TABLE [dbo].[Medications] CHECK CONSTRAINT [FK_Medications_Visits_VisitId]
GO
ALTER TABLE [dbo].[Names]  WITH CHECK ADD  CONSTRAINT [FK_Names_Persons_Id] FOREIGN KEY([Id])
REFERENCES [dbo].[Persons] ([Id])
GO
ALTER TABLE [dbo].[Names] CHECK CONSTRAINT [FK_Names_Persons_Id]
GO
ALTER TABLE [dbo].[Names]  WITH CHECK ADD  CONSTRAINT [FK_Names_Prefixes_PrefixId] FOREIGN KEY([PrefixId])
REFERENCES [dbo].[Prefixes] ([Id])
GO
ALTER TABLE [dbo].[Names] CHECK CONSTRAINT [FK_Names_Prefixes_PrefixId]
GO
ALTER TABLE [dbo].[Names]  WITH CHECK ADD  CONSTRAINT [FK_Names_Suffixes_SuffixId] FOREIGN KEY([SuffixId])
REFERENCES [dbo].[Suffixes] ([Id])
GO
ALTER TABLE [dbo].[Names] CHECK CONSTRAINT [FK_Names_Suffixes_SuffixId]
GO
ALTER TABLE [dbo].[NippleCustomOptions]  WITH CHECK ADD  CONSTRAINT [FK_NippleCustomOptions_Subscribers_SubscriberId] FOREIGN KEY([SubscriberId])
REFERENCES [dbo].[Subscribers] ([Id])
GO
ALTER TABLE [dbo].[NippleCustomOptions] CHECK CONSTRAINT [FK_NippleCustomOptions_Subscribers_SubscriberId]
GO
ALTER TABLE [dbo].[NippleObservations]  WITH CHECK ADD  CONSTRAINT [FK_NippleObservations_NippleCustomOptions_NippleCustomOptionId] FOREIGN KEY([NippleCustomOptionId])
REFERENCES [dbo].[NippleCustomOptions] ([Id])
GO
ALTER TABLE [dbo].[NippleObservations] CHECK CONSTRAINT [FK_NippleObservations_NippleCustomOptions_NippleCustomOptionId]
GO
ALTER TABLE [dbo].[NippleObservations]  WITH CHECK ADD  CONSTRAINT [FK_NippleObservations_NippleTypes_NippleTypeId] FOREIGN KEY([NippleTypeId])
REFERENCES [dbo].[NippleTypes] ([Id])
GO
ALTER TABLE [dbo].[NippleObservations] CHECK CONSTRAINT [FK_NippleObservations_NippleTypes_NippleTypeId]
GO
ALTER TABLE [dbo].[NippleObservations]  WITH CHECK ADD  CONSTRAINT [FK_NippleObservations_ParentEvaluations_EvaluationId] FOREIGN KEY([EvaluationId])
REFERENCES [dbo].[ParentEvaluations] ([Id])
GO
ALTER TABLE [dbo].[NippleObservations] CHECK CONSTRAINT [FK_NippleObservations_ParentEvaluations_EvaluationId]
GO
ALTER TABLE [dbo].[NippleShieldCustomOptions]  WITH CHECK ADD  CONSTRAINT [FK_NippleShieldCustomOptions_Subscribers_SubscriberId] FOREIGN KEY([SubscriberId])
REFERENCES [dbo].[Subscribers] ([Id])
GO
ALTER TABLE [dbo].[NippleShieldCustomOptions] CHECK CONSTRAINT [FK_NippleShieldCustomOptions_Subscribers_SubscriberId]
GO
ALTER TABLE [dbo].[PalateCustomOptions]  WITH CHECK ADD  CONSTRAINT [FK_PalateCustomOptions_Subscribers_SubscriberId] FOREIGN KEY([SubscriberId])
REFERENCES [dbo].[Subscribers] ([Id])
GO
ALTER TABLE [dbo].[PalateCustomOptions] CHECK CONSTRAINT [FK_PalateCustomOptions_Subscribers_SubscriberId]
GO
ALTER TABLE [dbo].[PalateObservations]  WITH CHECK ADD  CONSTRAINT [FK_PalateObservations_KidEvaluations_EvaluationId] FOREIGN KEY([EvaluationId])
REFERENCES [dbo].[KidEvaluations] ([Id])
GO
ALTER TABLE [dbo].[PalateObservations] CHECK CONSTRAINT [FK_PalateObservations_KidEvaluations_EvaluationId]
GO
ALTER TABLE [dbo].[PalateObservations]  WITH CHECK ADD  CONSTRAINT [FK_PalateObservations_PalateCustomOptions_PalateCustomOptionId] FOREIGN KEY([PalateCustomOptionId])
REFERENCES [dbo].[PalateCustomOptions] ([Id])
GO
ALTER TABLE [dbo].[PalateObservations] CHECK CONSTRAINT [FK_PalateObservations_PalateCustomOptions_PalateCustomOptionId]
GO
ALTER TABLE [dbo].[PalateObservations]  WITH CHECK ADD  CONSTRAINT [FK_PalateObservations_PalateTypes_PalateTypeId] FOREIGN KEY([PalateTypeId])
REFERENCES [dbo].[PalateTypes] ([Id])
GO
ALTER TABLE [dbo].[PalateObservations] CHECK CONSTRAINT [FK_PalateObservations_PalateTypes_PalateTypeId]
GO
ALTER TABLE [dbo].[ParentEvaluations]  WITH CHECK ADD  CONSTRAINT [FK_ParentEvaluations_BreastPumps_BreastPumpId] FOREIGN KEY([BreastPumpId])
REFERENCES [dbo].[BreastPumps] ([Id])
GO
ALTER TABLE [dbo].[ParentEvaluations] CHECK CONSTRAINT [FK_ParentEvaluations_BreastPumps_BreastPumpId]
GO
ALTER TABLE [dbo].[ParentEvaluations]  WITH CHECK ADD  CONSTRAINT [FK_ParentEvaluations_Evaluations_Id] FOREIGN KEY([Id])
REFERENCES [dbo].[Evaluations] ([Id])
GO
ALTER TABLE [dbo].[ParentEvaluations] CHECK CONSTRAINT [FK_ParentEvaluations_Evaluations_Id]
GO
ALTER TABLE [dbo].[ParentEvaluations]  WITH CHECK ADD  CONSTRAINT [FK_ParentEvaluations_FlangeSizeCustomOptions_FlangeSizeCustomOptionId] FOREIGN KEY([FlangeSizeCustomOptionId])
REFERENCES [dbo].[FlangeSizeCustomOptions] ([Id])
GO
ALTER TABLE [dbo].[ParentEvaluations] CHECK CONSTRAINT [FK_ParentEvaluations_FlangeSizeCustomOptions_FlangeSizeCustomOptionId]
GO
ALTER TABLE [dbo].[ParentEvaluations]  WITH CHECK ADD  CONSTRAINT [FK_ParentEvaluations_FlangeSizes_FlangeSizeId] FOREIGN KEY([FlangeSizeId])
REFERENCES [dbo].[FlangeSizes] ([Id])
GO
ALTER TABLE [dbo].[ParentEvaluations] CHECK CONSTRAINT [FK_ParentEvaluations_FlangeSizes_FlangeSizeId]
GO
ALTER TABLE [dbo].[ParentEvaluations]  WITH CHECK ADD  CONSTRAINT [FK_ParentEvaluations_NippleShieldCustomOptions_NippleShieldCustomOptionId] FOREIGN KEY([NippleShieldCustomOptionId])
REFERENCES [dbo].[NippleShieldCustomOptions] ([Id])
GO
ALTER TABLE [dbo].[ParentEvaluations] CHECK CONSTRAINT [FK_ParentEvaluations_NippleShieldCustomOptions_NippleShieldCustomOptionId]
GO
ALTER TABLE [dbo].[ParentEvaluations]  WITH CHECK ADD  CONSTRAINT [FK_ParentEvaluations_NippleShieldSizes_NippleShieldSizeId] FOREIGN KEY([NippleShieldSizeId])
REFERENCES [dbo].[NippleShieldSizes] ([Id])
GO
ALTER TABLE [dbo].[ParentEvaluations] CHECK CONSTRAINT [FK_ParentEvaluations_NippleShieldSizes_NippleShieldSizeId]
GO
ALTER TABLE [dbo].[ParentMedicalHistory]  WITH CHECK ADD  CONSTRAINT [FK_ParentMedicalHistory_CommunicationTypes_ConfidentialCommunicationMethodId] FOREIGN KEY([ConfidentialCommunicationMethodId])
REFERENCES [dbo].[CommunicationTypes] ([Id])
GO
ALTER TABLE [dbo].[ParentMedicalHistory] CHECK CONSTRAINT [FK_ParentMedicalHistory_CommunicationTypes_ConfidentialCommunicationMethodId]
GO
ALTER TABLE [dbo].[ParentMedicalHistory]  WITH CHECK ADD  CONSTRAINT [FK_ParentMedicalHistory_Visits_Id] FOREIGN KEY([Id])
REFERENCES [dbo].[Visits] ([Id])
GO
ALTER TABLE [dbo].[ParentMedicalHistory] CHECK CONSTRAINT [FK_ParentMedicalHistory_Visits_Id]
GO
ALTER TABLE [dbo].[PersonAddresses]  WITH CHECK ADD  CONSTRAINT [FK_PersonAddresses_Addresses_AddressId] FOREIGN KEY([AddressId])
REFERENCES [dbo].[Addresses] ([Id])
GO
ALTER TABLE [dbo].[PersonAddresses] CHECK CONSTRAINT [FK_PersonAddresses_Addresses_AddressId]
GO
ALTER TABLE [dbo].[PersonAddresses]  WITH CHECK ADD  CONSTRAINT [FK_PersonAddresses_Persons_PersonId] FOREIGN KEY([PersonId])
REFERENCES [dbo].[Persons] ([Id])
GO
ALTER TABLE [dbo].[PersonAddresses] CHECK CONSTRAINT [FK_PersonAddresses_Persons_PersonId]
GO
ALTER TABLE [dbo].[PersonEmails]  WITH CHECK ADD  CONSTRAINT [FK_PersonEmails_Emails_EmailId] FOREIGN KEY([EmailId])
REFERENCES [dbo].[Emails] ([Id])
GO
ALTER TABLE [dbo].[PersonEmails] CHECK CONSTRAINT [FK_PersonEmails_Emails_EmailId]
GO
ALTER TABLE [dbo].[PersonEmails]  WITH CHECK ADD  CONSTRAINT [FK_PersonEmails_Persons_PersonId] FOREIGN KEY([PersonId])
REFERENCES [dbo].[Persons] ([Id])
GO
ALTER TABLE [dbo].[PersonEmails] CHECK CONSTRAINT [FK_PersonEmails_Persons_PersonId]
GO
ALTER TABLE [dbo].[PersonImages]  WITH CHECK ADD  CONSTRAINT [FK_PersonImages_Images_ImageId] FOREIGN KEY([ImageId])
REFERENCES [dbo].[Images] ([Id])
GO
ALTER TABLE [dbo].[PersonImages] CHECK CONSTRAINT [FK_PersonImages_Images_ImageId]
GO
ALTER TABLE [dbo].[PersonImages]  WITH CHECK ADD  CONSTRAINT [FK_PersonImages_Persons_PersonId] FOREIGN KEY([PersonId])
REFERENCES [dbo].[Persons] ([Id])
GO
ALTER TABLE [dbo].[PersonImages] CHECK CONSTRAINT [FK_PersonImages_Persons_PersonId]
GO
ALTER TABLE [dbo].[PersonNotes]  WITH CHECK ADD  CONSTRAINT [FK_PersonNotes_NoteTypes_NoteTypeId] FOREIGN KEY([NoteTypeId])
REFERENCES [dbo].[NoteTypes] ([Id])
GO
ALTER TABLE [dbo].[PersonNotes] CHECK CONSTRAINT [FK_PersonNotes_NoteTypes_NoteTypeId]
GO
ALTER TABLE [dbo].[PersonNotes]  WITH CHECK ADD  CONSTRAINT [FK_PersonNotes_Persons_PersonId] FOREIGN KEY([PersonId])
REFERENCES [dbo].[Persons] ([Id])
GO
ALTER TABLE [dbo].[PersonNotes] CHECK CONSTRAINT [FK_PersonNotes_Persons_PersonId]
GO
ALTER TABLE [dbo].[PersonPersonTypes]  WITH CHECK ADD  CONSTRAINT [FK_PersonPersonTypes_Persons_PersonId] FOREIGN KEY([PersonId])
REFERENCES [dbo].[Persons] ([Id])
GO
ALTER TABLE [dbo].[PersonPersonTypes] CHECK CONSTRAINT [FK_PersonPersonTypes_Persons_PersonId]
GO
ALTER TABLE [dbo].[PersonPersonTypes]  WITH CHECK ADD  CONSTRAINT [FK_PersonPersonTypes_PersonTypes_PersonTypeId] FOREIGN KEY([PersonTypeId])
REFERENCES [dbo].[PersonTypes] ([Id])
GO
ALTER TABLE [dbo].[PersonPersonTypes] CHECK CONSTRAINT [FK_PersonPersonTypes_PersonTypes_PersonTypeId]
GO
ALTER TABLE [dbo].[PersonPhones]  WITH CHECK ADD  CONSTRAINT [FK_PersonPhones_Persons_PersonId] FOREIGN KEY([PersonId])
REFERENCES [dbo].[Persons] ([Id])
GO
ALTER TABLE [dbo].[PersonPhones] CHECK CONSTRAINT [FK_PersonPhones_Persons_PersonId]
GO
ALTER TABLE [dbo].[PersonPhones]  WITH CHECK ADD  CONSTRAINT [FK_PersonPhones_Phones_PhoneId] FOREIGN KEY([PhoneId])
REFERENCES [dbo].[Phones] ([Id])
GO
ALTER TABLE [dbo].[PersonPhones] CHECK CONSTRAINT [FK_PersonPhones_Phones_PhoneId]
GO
ALTER TABLE [dbo].[PersonProviders]  WITH CHECK ADD  CONSTRAINT [FK_PersonProviders_Persons_PersonId] FOREIGN KEY([PersonId])
REFERENCES [dbo].[Persons] ([Id])
GO
ALTER TABLE [dbo].[PersonProviders] CHECK CONSTRAINT [FK_PersonProviders_Persons_PersonId]
GO
ALTER TABLE [dbo].[PersonProviders]  WITH CHECK ADD  CONSTRAINT [FK_PersonProviders_SubscriberProviders_SubscriberProviderId] FOREIGN KEY([SubscriberProviderId])
REFERENCES [dbo].[SubscriberProviders] ([Id])
GO
ALTER TABLE [dbo].[PersonProviders] CHECK CONSTRAINT [FK_PersonProviders_SubscriberProviders_SubscriberProviderId]
GO
ALTER TABLE [dbo].[PersonProviderTypes]  WITH CHECK ADD  CONSTRAINT [FK_PersonProviderTypes_Persons_PersonId] FOREIGN KEY([PersonId])
REFERENCES [dbo].[Persons] ([Id])
GO
ALTER TABLE [dbo].[PersonProviderTypes] CHECK CONSTRAINT [FK_PersonProviderTypes_Persons_PersonId]
GO
ALTER TABLE [dbo].[PersonProviderTypes]  WITH CHECK ADD  CONSTRAINT [FK_PersonProviderTypes_ProviderTypes_ProviderTypeId] FOREIGN KEY([ProviderTypeId])
REFERENCES [dbo].[ProviderTypes] ([Id])
GO
ALTER TABLE [dbo].[PersonProviderTypes] CHECK CONSTRAINT [FK_PersonProviderTypes_ProviderTypes_ProviderTypeId]
GO
ALTER TABLE [dbo].[Persons]  WITH CHECK ADD  CONSTRAINT [FK_Persons_Genders_GenderId] FOREIGN KEY([GenderId])
REFERENCES [dbo].[Genders] ([Id])
GO
ALTER TABLE [dbo].[Persons] CHECK CONSTRAINT [FK_Persons_Genders_GenderId]
GO
ALTER TABLE [dbo].[Persons]  WITH CHECK ADD  CONSTRAINT [FK_Persons_Pronouns_PreferredPronounId] FOREIGN KEY([PreferredPronounId])
REFERENCES [dbo].[Pronouns] ([Id])
GO
ALTER TABLE [dbo].[Persons] CHECK CONSTRAINT [FK_Persons_Pronouns_PreferredPronounId]
GO
ALTER TABLE [dbo].[Phones]  WITH CHECK ADD  CONSTRAINT [FK_Phones_LineTypes_LineTypeId] FOREIGN KEY([LineTypeId])
REFERENCES [dbo].[LineTypes] ([Id])
GO
ALTER TABLE [dbo].[Phones] CHECK CONSTRAINT [FK_Phones_LineTypes_LineTypeId]
GO
ALTER TABLE [dbo].[Phones]  WITH CHECK ADD  CONSTRAINT [FK_Phones_PhoneTypes_PhoneTypeId] FOREIGN KEY([PhoneTypeId])
REFERENCES [dbo].[PhoneTypes] ([Id])
GO
ALTER TABLE [dbo].[Phones] CHECK CONSTRAINT [FK_Phones_PhoneTypes_PhoneTypeId]
GO
ALTER TABLE [dbo].[ProcedureCustomOptions]  WITH CHECK ADD  CONSTRAINT [FK_ProcedureCustomOptions_Subscribers_SubscriberId] FOREIGN KEY([SubscriberId])
REFERENCES [dbo].[Subscribers] ([Id])
GO
ALTER TABLE [dbo].[ProcedureCustomOptions] CHECK CONSTRAINT [FK_ProcedureCustomOptions_Subscribers_SubscriberId]
GO
ALTER TABLE [dbo].[Procedures]  WITH CHECK ADD  CONSTRAINT [FK_Procedures_Persons_PersonId] FOREIGN KEY([PersonId])
REFERENCES [dbo].[Persons] ([Id])
GO
ALTER TABLE [dbo].[Procedures] CHECK CONSTRAINT [FK_Procedures_Persons_PersonId]
GO
ALTER TABLE [dbo].[Procedures]  WITH CHECK ADD  CONSTRAINT [FK_Procedures_ProcedureCustomOptions_ProcedureCustomOptionId] FOREIGN KEY([ProcedureCustomOptionId])
REFERENCES [dbo].[ProcedureCustomOptions] ([Id])
GO
ALTER TABLE [dbo].[Procedures] CHECK CONSTRAINT [FK_Procedures_ProcedureCustomOptions_ProcedureCustomOptionId]
GO
ALTER TABLE [dbo].[Procedures]  WITH CHECK ADD  CONSTRAINT [FK_Procedures_ProcedureLocations_ProcedureLocationId] FOREIGN KEY([ProcedureLocationId])
REFERENCES [dbo].[ProcedureLocations] ([Id])
GO
ALTER TABLE [dbo].[Procedures] CHECK CONSTRAINT [FK_Procedures_ProcedureLocations_ProcedureLocationId]
GO
ALTER TABLE [dbo].[Procedures]  WITH CHECK ADD  CONSTRAINT [FK_Procedures_ProcedureTypes_ProcedureTypeId] FOREIGN KEY([ProcedureTypeId])
REFERENCES [dbo].[ProcedureTypes] ([Id])
GO
ALTER TABLE [dbo].[Procedures] CHECK CONSTRAINT [FK_Procedures_ProcedureTypes_ProcedureTypeId]
GO
ALTER TABLE [dbo].[ProductCustomOptions]  WITH CHECK ADD  CONSTRAINT [FK_ProductCustomOptions_Subscribers_SubscriberId] FOREIGN KEY([SubscriberId])
REFERENCES [dbo].[Subscribers] ([Id])
GO
ALTER TABLE [dbo].[ProductCustomOptions] CHECK CONSTRAINT [FK_ProductCustomOptions_Subscribers_SubscriberId]
GO
ALTER TABLE [dbo].[Products]  WITH CHECK ADD  CONSTRAINT [FK_Products_ParentEvaluations_EvaluationId] FOREIGN KEY([EvaluationId])
REFERENCES [dbo].[ParentEvaluations] ([Id])
GO
ALTER TABLE [dbo].[Products] CHECK CONSTRAINT [FK_Products_ParentEvaluations_EvaluationId]
GO
ALTER TABLE [dbo].[Products]  WITH CHECK ADD  CONSTRAINT [FK_Products_ProductCustomOptions_ProductCustomOptionId] FOREIGN KEY([ProductCustomOptionId])
REFERENCES [dbo].[ProductCustomOptions] ([Id])
GO
ALTER TABLE [dbo].[Products] CHECK CONSTRAINT [FK_Products_ProductCustomOptions_ProductCustomOptionId]
GO
ALTER TABLE [dbo].[Products]  WITH CHECK ADD  CONSTRAINT [FK_Products_ProductTypes_ProductTypeId] FOREIGN KEY([ProductTypeId])
REFERENCES [dbo].[ProductTypes] ([Id])
GO
ALTER TABLE [dbo].[Products] CHECK CONSTRAINT [FK_Products_ProductTypes_ProductTypeId]
GO
ALTER TABLE [dbo].[PumpBrandCustomOptions]  WITH CHECK ADD  CONSTRAINT [FK_PumpBrandCustomOptions_Subscribers_SubscriberId] FOREIGN KEY([SubscriberId])
REFERENCES [dbo].[Subscribers] ([Id])
GO
ALTER TABLE [dbo].[PumpBrandCustomOptions] CHECK CONSTRAINT [FK_PumpBrandCustomOptions_Subscribers_SubscriberId]
GO
ALTER TABLE [dbo].[ReferrerPersons]  WITH CHECK ADD  CONSTRAINT [FK_ReferrerPersons_Persons_PersonId] FOREIGN KEY([PersonId])
REFERENCES [dbo].[Persons] ([Id])
GO
ALTER TABLE [dbo].[ReferrerPersons] CHECK CONSTRAINT [FK_ReferrerPersons_Persons_PersonId]
GO
ALTER TABLE [dbo].[ReferrerPersons]  WITH CHECK ADD  CONSTRAINT [FK_ReferrerPersons_Persons_ReferrerId] FOREIGN KEY([ReferrerId])
REFERENCES [dbo].[Persons] ([Id])
GO
ALTER TABLE [dbo].[ReferrerPersons] CHECK CONSTRAINT [FK_ReferrerPersons_Persons_ReferrerId]
GO
ALTER TABLE [dbo].[ReferrerPersons]  WITH CHECK ADD  CONSTRAINT [FK_ReferrerPersons_Subscribers_SubscriberId] FOREIGN KEY([SubscriberId])
REFERENCES [dbo].[Subscribers] ([Id])
GO
ALTER TABLE [dbo].[ReferrerPersons] CHECK CONSTRAINT [FK_ReferrerPersons_Subscribers_SubscriberId]
GO
ALTER TABLE [dbo].[StatementConcerns]  WITH CHECK ADD  CONSTRAINT [FK_StatementConcerns_ConcernCustomOptions_ConcernCustomOptionId] FOREIGN KEY([ConcernCustomOptionId])
REFERENCES [dbo].[ConcernCustomOptions] ([Id])
GO
ALTER TABLE [dbo].[StatementConcerns] CHECK CONSTRAINT [FK_StatementConcerns_ConcernCustomOptions_ConcernCustomOptionId]
GO
ALTER TABLE [dbo].[StatementConcerns]  WITH CHECK ADD  CONSTRAINT [FK_StatementConcerns_Concerns_ConcernId] FOREIGN KEY([ConcernId])
REFERENCES [dbo].[Concerns] ([Id])
GO
ALTER TABLE [dbo].[StatementConcerns] CHECK CONSTRAINT [FK_StatementConcerns_Concerns_ConcernId]
GO
ALTER TABLE [dbo].[StatementConcerns]  WITH CHECK ADD  CONSTRAINT [FK_StatementConcerns_Statements_StatementId] FOREIGN KEY([StatementId])
REFERENCES [dbo].[Statements] ([Id])
GO
ALTER TABLE [dbo].[StatementConcerns] CHECK CONSTRAINT [FK_StatementConcerns_Statements_StatementId]
GO
ALTER TABLE [dbo].[Statements]  WITH CHECK ADD  CONSTRAINT [FK_Statements_Visits_Id] FOREIGN KEY([Id])
REFERENCES [dbo].[Visits] ([Id])
GO
ALTER TABLE [dbo].[Statements] CHECK CONSTRAINT [FK_Statements_Visits_Id]
GO
ALTER TABLE [dbo].[StoolColorCustomOptions]  WITH CHECK ADD  CONSTRAINT [FK_StoolColorCustomOptions_Subscribers_SubscriberId] FOREIGN KEY([SubscriberId])
REFERENCES [dbo].[Subscribers] ([Id])
GO
ALTER TABLE [dbo].[StoolColorCustomOptions] CHECK CONSTRAINT [FK_StoolColorCustomOptions_Subscribers_SubscriberId]
GO
ALTER TABLE [dbo].[StoolColors]  WITH CHECK ADD  CONSTRAINT [FK_StoolColors_KidEvaluations_EvaluationId] FOREIGN KEY([EvaluationId])
REFERENCES [dbo].[KidEvaluations] ([Id])
GO
ALTER TABLE [dbo].[StoolColors] CHECK CONSTRAINT [FK_StoolColors_KidEvaluations_EvaluationId]
GO
ALTER TABLE [dbo].[StoolColors]  WITH CHECK ADD  CONSTRAINT [FK_StoolColors_StoolColorCustomOptions_StoolColorCustomOptionId] FOREIGN KEY([StoolColorCustomOptionId])
REFERENCES [dbo].[StoolColorCustomOptions] ([Id])
GO
ALTER TABLE [dbo].[StoolColors] CHECK CONSTRAINT [FK_StoolColors_StoolColorCustomOptions_StoolColorCustomOptionId]
GO
ALTER TABLE [dbo].[StoolColors]  WITH CHECK ADD  CONSTRAINT [FK_StoolColors_StoolColorTypes_StoolColorTypeId] FOREIGN KEY([StoolColorTypeId])
REFERENCES [dbo].[StoolColorTypes] ([Id])
GO
ALTER TABLE [dbo].[StoolColors] CHECK CONSTRAINT [FK_StoolColors_StoolColorTypes_StoolColorTypeId]
GO
ALTER TABLE [dbo].[StoolConsistencies]  WITH CHECK ADD  CONSTRAINT [FK_StoolConsistencies_KidEvaluations_EvaluationId] FOREIGN KEY([EvaluationId])
REFERENCES [dbo].[KidEvaluations] ([Id])
GO
ALTER TABLE [dbo].[StoolConsistencies] CHECK CONSTRAINT [FK_StoolConsistencies_KidEvaluations_EvaluationId]
GO
ALTER TABLE [dbo].[StoolConsistencies]  WITH CHECK ADD  CONSTRAINT [FK_StoolConsistencies_StoolConsistencyCustomOptions_StoolConsistencyCustomOptionId] FOREIGN KEY([StoolConsistencyCustomOptionId])
REFERENCES [dbo].[StoolConsistencyCustomOptions] ([Id])
GO
ALTER TABLE [dbo].[StoolConsistencies] CHECK CONSTRAINT [FK_StoolConsistencies_StoolConsistencyCustomOptions_StoolConsistencyCustomOptionId]
GO
ALTER TABLE [dbo].[StoolConsistencies]  WITH CHECK ADD  CONSTRAINT [FK_StoolConsistencies_StoolConsistencyTypes_StoolConsistencyTypeId] FOREIGN KEY([StoolConsistencyTypeId])
REFERENCES [dbo].[StoolConsistencyTypes] ([Id])
GO
ALTER TABLE [dbo].[StoolConsistencies] CHECK CONSTRAINT [FK_StoolConsistencies_StoolConsistencyTypes_StoolConsistencyTypeId]
GO
ALTER TABLE [dbo].[StoolConsistencyCustomOptions]  WITH CHECK ADD  CONSTRAINT [FK_StoolConsistencyCustomOptions_Subscribers_SubscriberId] FOREIGN KEY([SubscriberId])
REFERENCES [dbo].[Subscribers] ([Id])
GO
ALTER TABLE [dbo].[StoolConsistencyCustomOptions] CHECK CONSTRAINT [FK_StoolConsistencyCustomOptions_Subscribers_SubscriberId]
GO
ALTER TABLE [dbo].[SubscriberBreastPumps]  WITH CHECK ADD  CONSTRAINT [FK_SubscriberBreastPumps_PumpBrands_BrandId] FOREIGN KEY([BrandId])
REFERENCES [dbo].[PumpBrands] ([Id])
GO
ALTER TABLE [dbo].[SubscriberBreastPumps] CHECK CONSTRAINT [FK_SubscriberBreastPumps_PumpBrands_BrandId]
GO
ALTER TABLE [dbo].[SubscriberBreastPumps]  WITH CHECK ADD  CONSTRAINT [FK_SubscriberBreastPumps_PumpTypes_TypeId] FOREIGN KEY([TypeId])
REFERENCES [dbo].[PumpTypes] ([Id])
GO
ALTER TABLE [dbo].[SubscriberBreastPumps] CHECK CONSTRAINT [FK_SubscriberBreastPumps_PumpTypes_TypeId]
GO
ALTER TABLE [dbo].[SubscriberBreastPumps]  WITH CHECK ADD  CONSTRAINT [FK_SubscriberBreastPumps_Subscribers_SubscriberId] FOREIGN KEY([SubscriberId])
REFERENCES [dbo].[Subscribers] ([Id])
GO
ALTER TABLE [dbo].[SubscriberBreastPumps] CHECK CONSTRAINT [FK_SubscriberBreastPumps_Subscribers_SubscriberId]
GO
ALTER TABLE [dbo].[SubscriberParents]  WITH CHECK ADD  CONSTRAINT [FK_SubscriberParents_Persons_ParentId] FOREIGN KEY([ParentId])
REFERENCES [dbo].[Persons] ([Id])
GO
ALTER TABLE [dbo].[SubscriberParents] CHECK CONSTRAINT [FK_SubscriberParents_Persons_ParentId]
GO
ALTER TABLE [dbo].[SubscriberParents]  WITH CHECK ADD  CONSTRAINT [FK_SubscriberParents_Subscribers_SubscriberId] FOREIGN KEY([SubscriberId])
REFERENCES [dbo].[Subscribers] ([Id])
GO
ALTER TABLE [dbo].[SubscriberParents] CHECK CONSTRAINT [FK_SubscriberParents_Subscribers_SubscriberId]
GO
ALTER TABLE [dbo].[Subscribers]  WITH CHECK ADD  CONSTRAINT [FK_Subscribers_Calendars_CalendarId] FOREIGN KEY([CalendarId])
REFERENCES [dbo].[Calendars] ([Id])
GO
ALTER TABLE [dbo].[Subscribers] CHECK CONSTRAINT [FK_Subscribers_Calendars_CalendarId]
GO
ALTER TABLE [dbo].[Subscribers]  WITH CHECK ADD  CONSTRAINT [FK_Subscribers_SubscriberUsers_AdminSubscriberUserId] FOREIGN KEY([AdminSubscriberUserId])
REFERENCES [dbo].[SubscriberUsers] ([Id])
GO
ALTER TABLE [dbo].[Subscribers] CHECK CONSTRAINT [FK_Subscribers_SubscriberUsers_AdminSubscriberUserId]
GO
ALTER TABLE [dbo].[Subscribers]  WITH CHECK ADD  CONSTRAINT [FK_Subscribers_SubscriptionTerms_SubscriptionTermId] FOREIGN KEY([SubscriptionTermId])
REFERENCES [dbo].[SubscriptionTerms] ([Id])
GO
ALTER TABLE [dbo].[Subscribers] CHECK CONSTRAINT [FK_Subscribers_SubscriptionTerms_SubscriptionTermId]
GO
ALTER TABLE [dbo].[Subscribers]  WITH CHECK ADD  CONSTRAINT [FK_Subscribers_WeightUnits_DefaultWeightUnitId] FOREIGN KEY([DefaultWeightUnitId])
REFERENCES [dbo].[WeightUnits] ([Id])
GO
ALTER TABLE [dbo].[Subscribers] CHECK CONSTRAINT [FK_Subscribers_WeightUnits_DefaultWeightUnitId]
GO
ALTER TABLE [dbo].[SubscriberSettings]  WITH CHECK ADD  CONSTRAINT [FK_SubscriberSettings_Subscribers_Id] FOREIGN KEY([Id])
REFERENCES [dbo].[Subscribers] ([Id])
GO
ALTER TABLE [dbo].[SubscriberSettings] CHECK CONSTRAINT [FK_SubscriberSettings_Subscribers_Id]
GO
ALTER TABLE [dbo].[SubscriberUsers]  WITH CHECK ADD  CONSTRAINT [FK_SubscriberUsers_Calendars_CalendarId] FOREIGN KEY([CalendarId])
REFERENCES [dbo].[Calendars] ([Id])
GO
ALTER TABLE [dbo].[SubscriberUsers] CHECK CONSTRAINT [FK_SubscriberUsers_Calendars_CalendarId]
GO
ALTER TABLE [dbo].[SubscriberUsers]  WITH CHECK ADD  CONSTRAINT [FK_SubscriberUsers_Subscribers_SubscriberId] FOREIGN KEY([SubscriberId])
REFERENCES [dbo].[Subscribers] ([Id])
GO
ALTER TABLE [dbo].[SubscriberUsers] CHECK CONSTRAINT [FK_SubscriberUsers_Subscribers_SubscriberId]
GO
ALTER TABLE [dbo].[SubscriberUsers]  WITH CHECK ADD  CONSTRAINT [FK_SubscriberUsers_Users_UserId] FOREIGN KEY([UserId])
REFERENCES [dbo].[Users] ([Id])
GO
ALTER TABLE [dbo].[SubscriberUsers] CHECK CONSTRAINT [FK_SubscriberUsers_Users_UserId]
GO
ALTER TABLE [dbo].[SuckCustomOptions]  WITH CHECK ADD  CONSTRAINT [FK_SuckCustomOptions_Subscribers_SubscriberId] FOREIGN KEY([SubscriberId])
REFERENCES [dbo].[Subscribers] ([Id])
GO
ALTER TABLE [dbo].[SuckCustomOptions] CHECK CONSTRAINT [FK_SuckCustomOptions_Subscribers_SubscriberId]
GO
ALTER TABLE [dbo].[SuckObservations]  WITH CHECK ADD  CONSTRAINT [FK_SuckObservations_KidEvaluations_EvaluationId] FOREIGN KEY([EvaluationId])
REFERENCES [dbo].[KidEvaluations] ([Id])
GO
ALTER TABLE [dbo].[SuckObservations] CHECK CONSTRAINT [FK_SuckObservations_KidEvaluations_EvaluationId]
GO
ALTER TABLE [dbo].[SuckObservations]  WITH CHECK ADD  CONSTRAINT [FK_SuckObservations_SuckCustomOptions_SuckCustomOptionId] FOREIGN KEY([SuckCustomOptionId])
REFERENCES [dbo].[SuckCustomOptions] ([Id])
GO
ALTER TABLE [dbo].[SuckObservations] CHECK CONSTRAINT [FK_SuckObservations_SuckCustomOptions_SuckCustomOptionId]
GO
ALTER TABLE [dbo].[SuckObservations]  WITH CHECK ADD  CONSTRAINT [FK_SuckObservations_SuckTypes_SuckTypeId] FOREIGN KEY([SuckTypeId])
REFERENCES [dbo].[SuckTypes] ([Id])
GO
ALTER TABLE [dbo].[SuckObservations] CHECK CONSTRAINT [FK_SuckObservations_SuckTypes_SuckTypeId]
GO
ALTER TABLE [dbo].[SupportPersons]  WITH CHECK ADD  CONSTRAINT [FK_SupportPersons_Persons_PersonId] FOREIGN KEY([PersonId])
REFERENCES [dbo].[Persons] ([Id])
GO
ALTER TABLE [dbo].[SupportPersons] CHECK CONSTRAINT [FK_SupportPersons_Persons_PersonId]
GO
ALTER TABLE [dbo].[SupportPersons]  WITH CHECK ADD  CONSTRAINT [FK_SupportPersons_Persons_SupporterId] FOREIGN KEY([SupporterId])
REFERENCES [dbo].[Persons] ([Id])
GO
ALTER TABLE [dbo].[SupportPersons] CHECK CONSTRAINT [FK_SupportPersons_Persons_SupporterId]
GO
ALTER TABLE [dbo].[SupportPersons]  WITH CHECK ADD  CONSTRAINT [FK_SupportPersons_Relationships_RelationshipId] FOREIGN KEY([RelationshipId])
REFERENCES [dbo].[Relationships] ([Id])
GO
ALTER TABLE [dbo].[SupportPersons] CHECK CONSTRAINT [FK_SupportPersons_Relationships_RelationshipId]
GO
ALTER TABLE [dbo].[TimedEvents]  WITH CHECK ADD  CONSTRAINT [FK_TimedEvents_BirthHistories_BirthHistoryId] FOREIGN KEY([BirthHistoryId])
REFERENCES [dbo].[BirthHistories] ([Id])
GO
ALTER TABLE [dbo].[TimedEvents] CHECK CONSTRAINT [FK_TimedEvents_BirthHistories_BirthHistoryId]
GO
ALTER TABLE [dbo].[TimedEvents]  WITH CHECK ADD  CONSTRAINT [FK_TimedEvents_TimedEventTypes_TimedEventTypeId] FOREIGN KEY([TimedEventTypeId])
REFERENCES [dbo].[TimedEventTypes] ([Id])
GO
ALTER TABLE [dbo].[TimedEvents] CHECK CONSTRAINT [FK_TimedEvents_TimedEventTypes_TimedEventTypeId]
GO
ALTER TABLE [dbo].[TongueCustomOptions]  WITH CHECK ADD  CONSTRAINT [FK_TongueCustomOptions_Subscribers_SubscriberId] FOREIGN KEY([SubscriberId])
REFERENCES [dbo].[Subscribers] ([Id])
GO
ALTER TABLE [dbo].[TongueCustomOptions] CHECK CONSTRAINT [FK_TongueCustomOptions_Subscribers_SubscriberId]
GO
ALTER TABLE [dbo].[TongueObservations]  WITH CHECK ADD  CONSTRAINT [FK_TongueObservations_KidEvaluations_EvaluationId] FOREIGN KEY([EvaluationId])
REFERENCES [dbo].[KidEvaluations] ([Id])
GO
ALTER TABLE [dbo].[TongueObservations] CHECK CONSTRAINT [FK_TongueObservations_KidEvaluations_EvaluationId]
GO
ALTER TABLE [dbo].[TongueObservations]  WITH CHECK ADD  CONSTRAINT [FK_TongueObservations_TongueCustomOptions_TongueCustomOptionId] FOREIGN KEY([TongueCustomOptionId])
REFERENCES [dbo].[TongueCustomOptions] ([Id])
GO
ALTER TABLE [dbo].[TongueObservations] CHECK CONSTRAINT [FK_TongueObservations_TongueCustomOptions_TongueCustomOptionId]
GO
ALTER TABLE [dbo].[TongueObservations]  WITH CHECK ADD  CONSTRAINT [FK_TongueObservations_TongueTypes_TongueTypeId] FOREIGN KEY([TongueTypeId])
REFERENCES [dbo].[TongueTypes] ([Id])
GO
ALTER TABLE [dbo].[TongueObservations] CHECK CONSTRAINT [FK_TongueObservations_TongueTypes_TongueTypeId]
GO
ALTER TABLE [dbo].[VisitChildConcerns]  WITH CHECK ADD  CONSTRAINT [FK_VisitChildConcerns_ChildConcerns_ChildConcernId] FOREIGN KEY([ChildConcernId])
REFERENCES [dbo].[ChildConcerns] ([Id])
GO
ALTER TABLE [dbo].[VisitChildConcerns] CHECK CONSTRAINT [FK_VisitChildConcerns_ChildConcerns_ChildConcernId]
GO
ALTER TABLE [dbo].[VisitChildConcerns]  WITH CHECK ADD  CONSTRAINT [FK_VisitChildConcerns_Persons_ChildId] FOREIGN KEY([ChildId])
REFERENCES [dbo].[Persons] ([Id])
GO
ALTER TABLE [dbo].[VisitChildConcerns] CHECK CONSTRAINT [FK_VisitChildConcerns_Persons_ChildId]
GO
ALTER TABLE [dbo].[VisitChildConcerns]  WITH CHECK ADD  CONSTRAINT [FK_VisitChildConcerns_Visits_VisitId] FOREIGN KEY([VisitId])
REFERENCES [dbo].[Visits] ([Id])
GO
ALTER TABLE [dbo].[VisitChildConcerns] CHECK CONSTRAINT [FK_VisitChildConcerns_Visits_VisitId]
GO
ALTER TABLE [dbo].[VisitChildNotes]  WITH CHECK ADD  CONSTRAINT [FK_VisitChildNotes_Persons_ChildId] FOREIGN KEY([ChildId])
REFERENCES [dbo].[Persons] ([Id])
GO
ALTER TABLE [dbo].[VisitChildNotes] CHECK CONSTRAINT [FK_VisitChildNotes_Persons_ChildId]
GO
ALTER TABLE [dbo].[VisitChildNotes]  WITH CHECK ADD  CONSTRAINT [FK_VisitChildNotes_Persons_EnteredByPersonId] FOREIGN KEY([EnteredByPersonId])
REFERENCES [dbo].[Persons] ([Id])
GO
ALTER TABLE [dbo].[VisitChildNotes] CHECK CONSTRAINT [FK_VisitChildNotes_Persons_EnteredByPersonId]
GO
ALTER TABLE [dbo].[VisitChildNotes]  WITH CHECK ADD  CONSTRAINT [FK_VisitChildNotes_Visits_VisitId] FOREIGN KEY([VisitId])
REFERENCES [dbo].[Visits] ([Id])
GO
ALTER TABLE [dbo].[VisitChildNotes] CHECK CONSTRAINT [FK_VisitChildNotes_Visits_VisitId]
GO
ALTER TABLE [dbo].[VisitNotes]  WITH CHECK ADD  CONSTRAINT [FK_VisitNotes_Persons_EnteredByPersonId] FOREIGN KEY([EnteredByPersonId])
REFERENCES [dbo].[Persons] ([Id])
GO
ALTER TABLE [dbo].[VisitNotes] CHECK CONSTRAINT [FK_VisitNotes_Persons_EnteredByPersonId]
GO
ALTER TABLE [dbo].[VisitNotes]  WITH CHECK ADD  CONSTRAINT [FK_VisitNotes_Visits_VisitId] FOREIGN KEY([VisitId])
REFERENCES [dbo].[Visits] ([Id])
GO
ALTER TABLE [dbo].[VisitNotes] CHECK CONSTRAINT [FK_VisitNotes_Visits_VisitId]
GO
ALTER TABLE [dbo].[VisitPostPartumScaleQuestionScores]  WITH CHECK ADD  CONSTRAINT [FK_VisitPostPartumScaleQuestionScores_PostPartumScaleQuestions_PostPartumScaleQuestionId] FOREIGN KEY([PostPartumScaleQuestionId])
REFERENCES [dbo].[PostPartumScaleQuestions] ([Id])
GO
ALTER TABLE [dbo].[VisitPostPartumScaleQuestionScores] CHECK CONSTRAINT [FK_VisitPostPartumScaleQuestionScores_PostPartumScaleQuestions_PostPartumScaleQuestionId]
GO
ALTER TABLE [dbo].[VisitPostPartumScaleQuestionScores]  WITH CHECK ADD  CONSTRAINT [FK_VisitPostPartumScaleQuestionScores_Visits_VisitId] FOREIGN KEY([VisitId])
REFERENCES [dbo].[Visits] ([Id])
GO
ALTER TABLE [dbo].[VisitPostPartumScaleQuestionScores] CHECK CONSTRAINT [FK_VisitPostPartumScaleQuestionScores_Visits_VisitId]
GO
ALTER TABLE [dbo].[Visits]  WITH CHECK ADD  CONSTRAINT [FK_Visits_AppointmentTypes_AppointmentTypeId] FOREIGN KEY([AppointmentTypeId])
REFERENCES [dbo].[AppointmentTypes] ([Id])
GO
ALTER TABLE [dbo].[Visits] CHECK CONSTRAINT [FK_Visits_AppointmentTypes_AppointmentTypeId]
GO
ALTER TABLE [dbo].[Visits]  WITH CHECK ADD  CONSTRAINT [FK_Visits_CommunicationTypes_PreferredCommunicationTypeId] FOREIGN KEY([PreferredCommunicationTypeId])
REFERENCES [dbo].[CommunicationTypes] ([Id])
GO
ALTER TABLE [dbo].[Visits] CHECK CONSTRAINT [FK_Visits_CommunicationTypes_PreferredCommunicationTypeId]
GO
ALTER TABLE [dbo].[Visits]  WITH CHECK ADD  CONSTRAINT [FK_Visits_Persons_ConsultantId] FOREIGN KEY([ConsultantId])
REFERENCES [dbo].[Persons] ([Id])
GO
ALTER TABLE [dbo].[Visits] CHECK CONSTRAINT [FK_Visits_Persons_ConsultantId]
GO
ALTER TABLE [dbo].[Visits]  WITH CHECK ADD  CONSTRAINT [FK_Visits_Persons_ParentId] FOREIGN KEY([ParentId])
REFERENCES [dbo].[Persons] ([Id])
GO
ALTER TABLE [dbo].[Visits] CHECK CONSTRAINT [FK_Visits_Persons_ParentId]
GO
ALTER TABLE [dbo].[Visits]  WITH CHECK ADD  CONSTRAINT [FK_Visits_SelfPayTypes_SelfPayTypeId] FOREIGN KEY([SelfPayTypeId])
REFERENCES [dbo].[SelfPayTypes] ([Id])
GO
ALTER TABLE [dbo].[Visits] CHECK CONSTRAINT [FK_Visits_SelfPayTypes_SelfPayTypeId]
GO
ALTER TABLE [dbo].[Visits]  WITH CHECK ADD  CONSTRAINT [FK_Visits_Subscribers_SubscriberId] FOREIGN KEY([SubscriberId])
REFERENCES [dbo].[Subscribers] ([Id])
GO
ALTER TABLE [dbo].[Visits] CHECK CONSTRAINT [FK_Visits_Subscribers_SubscriberId]
GO
ALTER TABLE [dbo].[Visits]  WITH CHECK ADD  CONSTRAINT [FK_Visits_VisitAcceptanceStatuses_VisitAcceptanceStatusId] FOREIGN KEY([VisitAcceptanceStatusId])
REFERENCES [dbo].[VisitAcceptanceStatuses] ([Id])
GO
ALTER TABLE [dbo].[Visits] CHECK CONSTRAINT [FK_Visits_VisitAcceptanceStatuses_VisitAcceptanceStatusId]
GO
ALTER TABLE [dbo].[Visits]  WITH CHECK ADD  CONSTRAINT [FK_Visits_VisitCancellationReasons_VisitCancellationReasonId] FOREIGN KEY([VisitCancellationReasonId])
REFERENCES [dbo].[VisitCancellationReasons] ([Id])
GO
ALTER TABLE [dbo].[Visits] CHECK CONSTRAINT [FK_Visits_VisitCancellationReasons_VisitCancellationReasonId]
GO
ALTER TABLE [dbo].[Visits]  WITH CHECK ADD  CONSTRAINT [FK_Visits_VisitStatuses_VisitStatusId] FOREIGN KEY([VisitStatusId])
REFERENCES [dbo].[VisitStatuses] ([Id])
GO
ALTER TABLE [dbo].[Visits] CHECK CONSTRAINT [FK_Visits_VisitStatuses_VisitStatusId]
GO
ALTER TABLE [dbo].[Visits]  WITH CHECK ADD  CONSTRAINT [FK_Visits_VisitTypes_VisitTypeId] FOREIGN KEY([VisitTypeId])
REFERENCES [dbo].[VisitTypes] ([Id])
GO
ALTER TABLE [dbo].[Visits] CHECK CONSTRAINT [FK_Visits_VisitTypes_VisitTypeId]
GO
