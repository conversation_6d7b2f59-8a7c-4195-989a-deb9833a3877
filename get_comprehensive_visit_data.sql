-- Comprehensive Visit Data Query with Full Evaluation Details
-- This query provides complete visit information including detailed evaluation data

USE [MilkNotes_local]
GO

SELECT 
    -- Basic Visit Information
    v.Id AS VisitId,
    v.StartDateTime,
    v.EndDateTime,
    v.ClosedDateTime,
    
    -- Visit Status
    vt.Name AS VisitType,
    vs.Name AS VisitStatus,
    v.IsCancelled,
    v.IsIntakeComplete,
    
    -- People
    CONCAT(parent_names.First, ' ', parent_names.Last) AS ParentName,
    CONCAT(consultant_names.First, ' ', consultant_names.Last) AS ConsultantName,
    
    -- Appointment Details
    at.Name AS AppointmentType,
    cl.Name AS ConsultLocation,
    v.<PERSON>,
    
    -- Payment Method
    CASE 
        WHEN v.IsInsurance = 1 THEN 'Insurance'
        WHEN v.IsMedicaid = 1 THEN 'Medicaid'
        WHEN v.IsHsaFsa = 1 THEN 'HSA/FSA'
        WHEN v.IsSelfPay = 1 THEN 'Self Pay'
        ELSE 'Unknown'
    END AS PaymentMethod,
    
    -- Key Visit Notes
    v.ParentConcerns,
    v.MedicalReportNotes,
    
    -- Birth History Summary
    (SELECT COUNT(*) 
     FROM [dbo].[Kids] k 
     INNER JOIN [dbo].[BirthHistories] bh ON k.Id = bh.Id 
     WHERE k.ParentId = v.ParentId) AS ChildrenWithBirthHistoryCount,
    
    -- Statement Goals
    s.ShortTermBreastfeedingGoals,
    s.LongTermBreastfeedingGoals,
    s.Employment,
    s.IsRetrningToWork,
    
    -- Evaluation Summary
    (SELECT COUNT(*) FROM [dbo].[Evaluations] e WHERE e.VisitId = v.Id) AS EvaluationCount,
    
    -- Kid Evaluation Details
    (SELECT TOP 1 CONCAT(
        CASE WHEN ke.StoolsPerDay IS NOT NULL THEN 'Stools: ' + CAST(ke.StoolsPerDay AS VARCHAR) + '/day; ' ELSE '' END,
        CASE WHEN ke.VoidsPerDay IS NOT NULL THEN 'Voids: ' + CAST(ke.VoidsPerDay AS VARCHAR) + '/day; ' ELSE '' END,
        CASE WHEN ke.NumFeedsPerDay IS NOT NULL THEN 'Feeds: ' + CAST(ke.NumFeedsPerDay AS VARCHAR) + '/day; ' ELSE '' END,
        CASE WHEN ke.FeedingDurationMinutes IS NOT NULL THEN 'Duration: ' + ke.FeedingDurationMinutes + 'min; ' ELSE '' END,
        CASE WHEN ke.ExpressedMilkByBottlePerDay IS NOT NULL THEN 'Expressed: ' + ke.ExpressedMilkByBottlePerDay + '; ' ELSE '' END,
        CASE WHEN ke.FormulaPerDay IS NOT NULL THEN 'Formula: ' + ke.FormulaPerDay ELSE '' END
    )
    FROM [dbo].[Evaluations] e 
    INNER JOIN [dbo].[KidEvaluations] ke ON e.Id = ke.Id 
    WHERE e.VisitId = v.Id) AS KidEvaluationSummary,
    
    -- Parent Evaluation Details
    (SELECT TOP 1 CONCAT(
        CASE WHEN fs.Name IS NOT NULL THEN 'Flange: ' + fs.Name + '; ' ELSE '' END,
        CASE WHEN nss.Name IS NOT NULL THEN 'Shield: ' + nss.Name + '; ' ELSE '' END,
        CASE WHEN pe.OtherComments IS NOT NULL THEN 'Notes: ' + pe.OtherComments ELSE '' END
    )
    FROM [dbo].[Evaluations] e 
    INNER JOIN [dbo].[ParentEvaluations] pe ON e.Id = pe.Id 
    LEFT JOIN [dbo].[FlangeSizes] fs ON pe.FlangeSizeId = fs.Id
    LEFT JOIN [dbo].[NippleShieldSizes] nss ON pe.NippleShieldSizeId = nss.Id
    WHERE e.VisitId = v.Id) AS ParentEvaluationSummary,
    
    -- Physical Observations Summary
    (SELECT STRING_AGG(CAST(observation_type AS NVARCHAR(MAX)), '; ')
    FROM (
        SELECT DISTINCT 'Appearance: ' + app.Name AS observation_type
        FROM [dbo].[Evaluations] e 
        INNER JOIN [dbo].[KidEvaluations] ke ON e.Id = ke.Id
        INNER JOIN [dbo].[Appearances] a ON ke.Id = a.EvaluationId
        INNER JOIN [dbo].[AppearanceTypes] app ON a.AppearanceTypeId = app.Id
        WHERE e.VisitId = v.Id
        
        UNION
        
        SELECT DISTINCT 'Breast: ' + bt.Name
        FROM [dbo].[Evaluations] e 
        INNER JOIN [dbo].[KidEvaluations] ke ON e.Id = ke.Id
        INNER JOIN [dbo].[BreastObservations] bo ON ke.Id = bo.EvaluationId
        INNER JOIN [dbo].[BreastTypes] bt ON bo.BreastTypeId = bt.Id
        WHERE e.VisitId = v.Id
        
        UNION
        
        SELECT DISTINCT 'Feeding: ' + fmt.Name
        FROM [dbo].[Evaluations] e 
        INNER JOIN [dbo].[KidEvaluations] ke ON e.Id = ke.Id
        INNER JOIN [dbo].[FeedingMethods] fm ON ke.Id = fm.EvaluationId
        INNER JOIN [dbo].[FeedingMethodTypes] fmt ON fm.FeedingMethodTypeId = fmt.Id
        WHERE e.VisitId = v.Id
    ) observations) AS PhysicalObservationsSummary,
    
    -- Diagnoses Summary
    (SELECT COUNT(*) FROM [dbo].[Diagnoses] d WHERE d.VisitId = v.Id) AS DiagnosesCount,
    (SELECT STRING_AGG(CAST(dt.Name AS NVARCHAR(MAX)), '; ')
     FROM [dbo].[Diagnoses] d 
     INNER JOIN [dbo].[DiagnosisTypes] dt ON d.DiagnosisTypeId = dt.Id
     WHERE d.VisitId = v.Id) AS DiagnosesList,
    
    -- ICD10 Summary
    (SELECT COUNT(*) FROM [dbo].[ICD10s] i WHERE i.VisitId = v.Id) AS ICD10Count,
    (SELECT STRING_AGG(CAST(CONCAT(ic.Code, ' - ', ic.Name) AS NVARCHAR(MAX)), '; ')
     FROM [dbo].[ICD10s] i 
     INNER JOIN [dbo].[ICD10Codes] ic ON i.ICD10CodeId = ic.Id
     WHERE i.VisitId = v.Id) AS ICD10List,
    
    -- Care Plans Summary
    (SELECT COUNT(DISTINCT cp.Id) 
     FROM [dbo].[Evaluations] e 
     INNER JOIN [dbo].[CarePlans] cp ON e.Id = cp.Id 
     WHERE e.VisitId = v.Id) AS CarePlansCount

FROM [dbo].[Visits] v
    
    -- Essential joins
    LEFT JOIN [dbo].[VisitTypes] vt ON v.VisitTypeId = vt.Id
    LEFT JOIN [dbo].[VisitStatuses] vs ON v.VisitStatusId = vs.Id
    
    -- Parent information
    LEFT JOIN [dbo].[Persons] parent_persons ON v.ParentId = parent_persons.Id
    LEFT JOIN [dbo].[Names] parent_names ON parent_persons.Id = parent_names.Id
    
    -- Consultant information
    LEFT JOIN [dbo].[Persons] consultant_persons ON v.ConsultantId = consultant_persons.Id
    LEFT JOIN [dbo].[Names] consultant_names ON consultant_persons.Id = consultant_names.Id
    
    -- Appointment and location
    LEFT JOIN [dbo].[AppointmentTypes] at ON v.AppointmentTypeId = at.Id
    LEFT JOIN [dbo].[ConsultLocations] cl ON at.ConsultLocationId = cl.Id
    
    -- Statements (1:1 with visits)
    LEFT JOIN [dbo].[Statements] s ON v.Id = s.Id

ORDER BY v.StartDateTime DESC;
