-- Query to get all visit data with comprehensive related information
-- This query retrieves all visits along with parent, consultant, appointment, and status details

USE [MilkNotes_local]
GO

SELECT 
    -- Visit basic information
    v.Id AS VisitId,
    v.StartDateTime,
    v.EndDateTime,
    v.ClosedDateTime,
    v.CreateDate AS VisitCreateDate,
    v.EditDate AS VisitEditDate,
    v.EditReason,
    v.DistanceTraveled,
    v.Price,
    v.PostBuffer,
    v.PreBuffer,
    
    -- Visit status and type information
    vt.Name AS VisitType,
    vs.Name AS VisitStatus,
    vas.Name AS VisitAcceptanceStatus,
    v.VisitNotAcceptedReason,
    v.IsCancelled,
    vcr.Name AS VisitCancellationReason,
    v.VisitCancellationOtherReason,
    v.IsIntakeComplete,
    
    -- Payment information
    v.IsHsaFsa,
    v.IsInsurance,
    v.IsMedicaid,
    v.IsSelfPay,
    spt.Name AS SelfPayType,
    
    -- HIPAA and communication
    v.HasAgreedToHipaaTerms,
    v.<PERSON>g<PERSON><PERSON><PERSON><PERSON><PERSON>aaConsent,
    ct.Name AS PreferredCommunicationType,
    
    -- Parent information
    parent_names.First AS ParentFirstName,
    parent_names.Middle AS ParentMiddleName,
    parent_names.Last AS ParentLastName,
    parent_persons.BirthDate AS ParentBirthDate,
    parent_genders.Name AS ParentGender,
    parent_persons.SSN AS ParentSSN,
    parent_persons.Allergies AS ParentAllergies,
    parent_persons.MedicaidNumber AS ParentMedicaidNumber,
    v.ParentHistoryNotes,
    v.ParentProfileNotes,
    v.ParentConcerns,
    
    -- Consultant information
    consultant_names.First AS ConsultantFirstName,
    consultant_names.Middle AS ConsultantMiddleName,
    consultant_names.Last AS ConsultantLastName,
    consultant_persons.BirthDate AS ConsultantBirthDate,
    consultant_genders.Name AS ConsultantGender,
    
    -- Appointment type information
    at.Name AS AppointmentType,
    at.Description AS AppointmentDescription,
    at.DefaultDuration AS AppointmentDefaultDuration,
    at.Color AS AppointmentColor,
    at.IsPublicFacing AS AppointmentIsPublicFacing,
    cl.Name AS ConsultLocation,
    
    -- Visit notes
    v.MedicalReportNotes,
    
    -- Subscriber information
    s.Id AS SubscriberId

FROM [dbo].[Visits] v
    
    -- Join with visit lookup tables
    LEFT JOIN [dbo].[VisitTypes] vt ON v.VisitTypeId = vt.Id
    LEFT JOIN [dbo].[VisitStatuses] vs ON v.VisitStatusId = vs.Id
    LEFT JOIN [dbo].[VisitAcceptanceStatuses] vas ON v.VisitAcceptanceStatusId = vas.Id
    LEFT JOIN [dbo].[VisitCancellationReasons] vcr ON v.VisitCancellationReasonId = vcr.Id
    LEFT JOIN [dbo].[SelfPayTypes] spt ON v.SelfPayTypeId = spt.Id
    LEFT JOIN [dbo].[CommunicationTypes] ct ON v.PreferredCommunicationTypeId = ct.Id
    
    -- Join with parent information
    LEFT JOIN [dbo].[Persons] parent_persons ON v.ParentId = parent_persons.Id
    LEFT JOIN [dbo].[Names] parent_names ON parent_persons.Id = parent_names.Id
    LEFT JOIN [dbo].[Genders] parent_genders ON parent_persons.GenderId = parent_genders.Id
    
    -- Join with consultant information
    LEFT JOIN [dbo].[Persons] consultant_persons ON v.ConsultantId = consultant_persons.Id
    LEFT JOIN [dbo].[Names] consultant_names ON consultant_persons.Id = consultant_names.Id
    LEFT JOIN [dbo].[Genders] consultant_genders ON consultant_persons.GenderId = consultant_genders.Id
    
    -- Join with appointment type and location
    LEFT JOIN [dbo].[AppointmentTypes] at ON v.AppointmentTypeId = at.Id
    LEFT JOIN [dbo].[ConsultLocations] cl ON at.ConsultLocationId = cl.Id
    
    -- Join with subscriber
    LEFT JOIN [dbo].[Subscribers] s ON v.SubscriberId = s.Id

ORDER BY v.StartDateTime DESC;
